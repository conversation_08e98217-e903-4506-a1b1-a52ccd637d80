/**
 * Service for handling organization-related API operations
 */

import { apiClient } from './client';
import {
  OrganizationDetailResponse,
  OrganizationShortResponse,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
} from '@/types/organization.types';
import { ApiResponse } from '@/types/api/common';

/**
 * Service class for organization management operations
 */
export class OrganizationService {
  private static BASE_URL = '/v1/organization';

  /**
   * Get a single organization by ID
   * @param id Organization ID
   * @returns Promise with organization detail data
   */
  static async getOrganizationById(id: number): Promise<OrganizationDetailResponse> {
    try {
      return await apiClient.get<OrganizationDetailResponse>(`${this.BASE_URL}/${id}`);
    } catch (error) {
      console.error('Error fetching organization:', error);
      throw error;
    }
  }

  /**
   * Get all organizations with detailed information
   * @returns Promise with array of organization detail data
   */
  static async getAllOrganizationsDetail(): Promise<OrganizationDetailResponse[]> {
    try {
      return await apiClient.get<OrganizationDetailResponse[]>(this.BASE_URL);
    } catch (error) {
      console.error('Error fetching organizations:', error);
      throw error;
    }
  }

  /**
   * Get all organizations with short information
   * @returns Promise with array of organization short data
   */
  static async getAllOrganizationsShort(): Promise<OrganizationShortResponse[]> {
    try {
      return await apiClient.get<OrganizationShortResponse[]>(`${this.BASE_URL}/short`);
    } catch (error) {
      console.error('Error fetching organizations (short):', error);
      throw error;
    }
  }

  /**
   * Create a new organization
   * @param data Organization data
   * @returns Promise with created organization data
   */
  static async createOrganization(data: CreateOrganizationRequest): Promise<OrganizationDetailResponse> {
    try {
      return await apiClient.post<OrganizationDetailResponse>(this.BASE_URL, data);
    } catch (error) {
      console.error('Error creating organization:', error);
      throw error;
    }
  }

  /**
   * Update an existing organization
   * @param id Organization ID
   * @param data Updated organization data
   * @returns Promise with updated organization data
   */
  static async updateOrganization(id: number, data: UpdateOrganizationRequest): Promise<OrganizationDetailResponse> {
    try {
      return await apiClient.put<OrganizationDetailResponse>(`${this.BASE_URL}/${id}`, data);
    } catch (error) {
      console.error('Error updating organization:', error);
      throw error;
    }
  }

  /**
   * Delete an organization
   * @param id Organization ID
   * @returns Promise that resolves when organization is deleted
   */
  static async deleteOrganization(id: number): Promise<void> {
    try {
      return await apiClient.delete<void>(`${this.BASE_URL}/${id}`);
    } catch (error) {
      console.error('Error deleting organization:', error);
      throw error;
    }
  }
}
