/**
 * Service for handling location-related API operations
 * Integrated with backend location API endpoints
 */

import { apiClient } from './client';
import {
  LocationDTO,
  AddressDTO,
  CreateLocationRequest,
  UpdateLocationRequest,
  Location,
  LegacyCreateLocationRequest,
  LegacyUpdateLocationRequest,
} from '@/types/location.types';
import { ApiResponse, PaginatedResponse, QueryParams } from '@/types/api/common';

/**
 * Data transformation utilities
 */

/**
 * Transform form data to backend LocationDTO format
 */
function transformFormDataToLocationDTO(formData: any, organizationId: number): CreateLocationRequest {
  return {
    name: formData.name,
    timezone: formData.timezone,
    organizationId: organizationId,
    address: {
      addressLine1: formData.addressLine1,
      addressLine2: formData.addressLine2 || undefined,
      city: formData.city,
      state: formData.state,
      countryCode: formData.countryCode,
      postalCode: formData.postalCode || undefined,
      latitude: formData.latitude || undefined,
      longitude: formData.longitude || undefined,
    },
    primary: formData.primary || false,
  };
}

/**
 * Transform backend LocationDTO to frontend display format
 */
function transformLocationDTOToDisplayFormat(locationDTO: LocationDTO): any {
  return {
    id: locationDTO.id?.toString() || '',
    name: locationDTO.name,
    timezone: locationDTO.timezone,
    organizationId: locationDTO.organizationId,
    addressLine1: locationDTO.address.addressLine1,
    addressLine2: locationDTO.address.addressLine2,
    city: locationDTO.address.city,
    state: locationDTO.address.state,
    countryCode: locationDTO.address.countryCode,
    postalCode: locationDTO.address.postalCode,
    latitude: locationDTO.address.latitude,
    longitude: locationDTO.address.longitude,
    primary: locationDTO.primary, // Use 'primary' field from backend
  };
}

/**
 * Transform form data to update request format
 */
function transformFormDataToUpdateRequest(formData: any, organizationId: number): UpdateLocationRequest {
  return {
    name: formData.name,
    timezone: formData.timezone,
    organizationId: organizationId,
    address: {
      addressLine1: formData.addressLine1,
      addressLine2: formData.addressLine2 || undefined,
      city: formData.city,
      state: formData.state,
      countryCode: formData.countryCode,
      postalCode: formData.postalCode || undefined,
      latitude: formData.latitude || undefined,
      longitude: formData.longitude || undefined,
    },
    primary: formData.primary || false,
  };
}

/**
 * Service class for location management operations
 */
export class LocationService {
  private static BASE_URL = '/v1/organization';

  /**
   * Get all locations or by organization ID
   * @param organizationId Optional organization ID to filter locations
   * @returns Promise with location data
   */
  static async getLocations(organizationId?: number): Promise<LocationDTO[]> {
    try {
      const url = `${this.BASE_URL}/${organizationId}/locations`;

      return await apiClient.get<LocationDTO[]>(url);
    } catch (error) {
      console.error('Error fetching locations:', error);
      throw error;
    }
  }

  /**
   * Get a paginated list of locations (for backward compatibility)
   * @param params Query parameters for pagination, sorting, and filtering
   * @returns Promise with paginated location data
   */
  static async getLocationsPaginated(params?: QueryParams): Promise<PaginatedResponse<any>> {
    try {
      const organizationId = params?.organizationId as number;
      const locations = await this.getLocations(organizationId);

      // Transform to display format
      let transformedLocations = locations.map(transformLocationDTOToDisplayFormat);

      // Apply client-side sorting
      if (params?.sort) {
        const sortField = params.sort.startsWith('-')
          ? params.sort.substring(1)
          : params.sort;
        const sortOrder = params.sort.startsWith('-') ? -1 : 1;

        transformedLocations.sort((a: any, b: any) => {
          let aValue = a[sortField];
          let bValue = b[sortField];

          // Handle null/undefined values
          if (aValue == null && bValue == null) return 0;
          if (aValue == null) return 1; // Always put nulls at the end
          if (bValue == null) return -1; // Always put nulls at the end

          // Handle boolean values (for isPrimary)
          if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
            if (aValue === bValue) return 0;
            // For ascending: true (1) comes after false (0), so true > false
            // For descending: false comes after true
            return sortOrder * (aValue ? 1 : -1);
          }

          // Handle string values (case-insensitive)
          if (typeof aValue === 'string' && typeof bValue === 'string') {
            const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
            return sortOrder * comparison;
          }

          // Handle numeric values
          if (typeof aValue === 'number' && typeof bValue === 'number') {
            return sortOrder * (aValue - bValue);
          }

          // Default comparison - convert to string and compare
          const aStr = String(aValue).toLowerCase();
          const bStr = String(bValue).toLowerCase();
          const comparison = aStr.localeCompare(bStr);
          return sortOrder * comparison;
        });
      } else {
        // Default sort by name ascending
        transformedLocations.sort((a: any, b: any) => {
          const aName = (a.name || '').toLowerCase();
          const bName = (b.name || '').toLowerCase();
          return aName.localeCompare(bName);
        });
      }



      // Apply client-side pagination
      const page = params?.page || 1;
      const limit = params?.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      const paginatedData = transformedLocations.slice(startIndex, endIndex);

      return {
        data: paginatedData,
        total: transformedLocations.length,
        page: page,
        limit: limit,
        totalPages: Math.ceil(transformedLocations.length / limit)
      };
    } catch (error) {
      console.error('Error fetching paginated locations:', error);
      throw error;
    }
  }

  /**
   * Get a location by ID
   * @param id Location ID
   * @returns Promise with location data
   */
  static async getLocationById(id: number): Promise<LocationDTO> {
    try {
      return await apiClient.get<LocationDTO>(`${this.BASE_URL}/${organizationId}/locations/${id}`);
    } catch (error) {
      console.error(`Error fetching location with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new location
   * @param data Location data (form data format)
   * @param organizationId Organization ID for the location
   * @returns Promise with created location data
   */
  static async createLocation(data: any, organizationId: number = 1): Promise<LocationDTO> {
    try {
      const locationData = transformFormDataToLocationDTO(data, organizationId);
      return await apiClient.post<LocationDTO>(`${this.BASE_URL}/${organizationId}/locations`, locationData);
    } catch (error) {
      console.error('Error creating location:', error);
      throw error;
    }
  }

  /**
   * Create a new location (legacy method for backward compatibility)
   * @param data Location data in legacy format
   * @returns Promise with created location data
   */
  static async createLocationLegacy(data: LegacyCreateLocationRequest): Promise<ApiResponse<Location>> {
    try {
      // Transform legacy format to new format
      const organizationId = parseInt(data.organization_id || '1');
      const transformedData = {
        name: data.name,
        timezone: 'UTC', // Default timezone
        addressLine1: data.address_line1,
        addressLine2: data.address_line2,
        city: data.city,
        state: data.state,
        countryCode: data.country.length === 2 ? data.country : 'US', // Convert to country code
        postalCode: data.postal_code,
        isPrimary: data.is_primary,
      };

      const createdLocation = await this.createLocation(transformedData, organizationId);

      // Transform back to legacy format for response
      return {
        data: {
          id: createdLocation.id?.toString() || '',
          name: createdLocation.name,
          address_line1: createdLocation.address.addressLine1,
          address_line2: createdLocation.address.addressLine2 || null,
          city: createdLocation.address.city,
          state: createdLocation.address.state,
          postal_code: createdLocation.address.postalCode || '',
          country: createdLocation.address.countryCode,
          is_primary: createdLocation.isPrimary,
          organization_id: createdLocation.organizationId.toString(),
          created_by_ip: '***********',
          created_by_user: '<EMAIL>',
          created_date: new Date().toISOString(),
          last_modified_by_ip: '***********',
          last_modified_by_user: '<EMAIL>',
          last_modified_date: new Date().toISOString(),
          version: 1,
          active: true
        },
        message: 'Location created successfully',
        status: 201
      };
    } catch (error) {
      console.error('Error creating location (legacy):', error);
      throw error;
    }
  }

  /**
   * Update an existing location
   * @param id Location ID
   * @param data Updated location data (form data format)
   * @param organizationId Organization ID for the location
   * @returns Promise with updated location data
   */
  static async updateLocation(id: number, data: any, organizationId: number = 1): Promise<LocationDTO> {
    try {
      const locationData = transformFormDataToUpdateRequest(data, organizationId);
      return await apiClient.put<LocationDTO>(`${this.BASE_URL}/${organizationId}/locations/${id}`, locationData);
    } catch (error) {
      console.error(`Error updating location with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update an existing location (legacy method for backward compatibility)
   * @param id Location ID
   * @param data Updated location data in legacy format
   * @returns Promise with updated location data
   */
  static async updateLocationLegacy(id: string, data: LegacyUpdateLocationRequest): Promise<ApiResponse<Location>> {
    try {
      const locationId = parseInt(id);
      const organizationId = parseInt(data.organization_id || '1');

      // Transform legacy format to new format
      const transformedData = {
        name: data.name,
        timezone: 'UTC', // Default timezone if not provided
        addressLine1: data.address_line1,
        addressLine2: data.address_line2,
        city: data.city,
        state: data.state,
        countryCode: data.country && data.country.length === 2 ? data.country : 'US',
        postalCode: data.postal_code,
        isPrimary: data.is_primary,
      };

      const updatedLocation = await this.updateLocation(locationId, transformedData, organizationId);

      // Transform back to legacy format for response
      return {
        data: {
          id: updatedLocation.id?.toString() || '',
          name: updatedLocation.name,
          address_line1: updatedLocation.address.addressLine1,
          address_line2: updatedLocation.address.addressLine2 || null,
          city: updatedLocation.address.city,
          state: updatedLocation.address.state,
          postal_code: updatedLocation.address.postalCode || '',
          country: updatedLocation.address.countryCode,
          is_primary: updatedLocation.isPrimary,
          organization_id: updatedLocation.organizationId.toString(),
          created_by_ip: '***********',
          created_by_user: '<EMAIL>',
          created_date: new Date().toISOString(),
          last_modified_by_ip: '***********',
          last_modified_by_user: '<EMAIL>',
          last_modified_date: new Date().toISOString(),
          version: 1,
          active: true
        },
        message: 'Location updated successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error updating location with ID ${id} (legacy):`, error);
      throw error;
    }
  }

  /**
   * Delete a location
   * @param id Location ID
   * @returns Promise with deletion confirmation
   */
  static async deleteLocation(id: number, organizationId: number): Promise<void> {
    try {
      await apiClient.delete<void>(`${this.BASE_URL}/${organizationId}/locations/${id}`);
    } catch (error) {
      console.error(`Error deleting location with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a location (legacy method for backward compatibility)
   * @param id Location ID
   * @returns Promise with deletion confirmation
   */
  static async deleteLocationLegacy(id: string): Promise<ApiResponse<void>> {
    try {
      const locationId = parseInt(id);
      await this.deleteLocation(locationId);

      return {
        data: undefined,
        message: 'Location deleted successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error deleting location with ID ${id} (legacy):`, error);
      throw error;
    }
  }

  /**
   * Set a location as primary
   * @param id Location ID to set as primary
   * @returns Promise with updated location data
   */
  static async setPrimaryLocation(id: number, organizationId: number): Promise<LocationDTO> {
    try {
      return await apiClient.put<LocationDTO>(`${this.BASE_URL}/${organizationId}/locations/${id}/set-primary`, {});
    } catch (error) {
      console.error(`Error setting location with ID ${id} as primary:`, error);
      throw error;
    }
  }

  /**
   * Set a location as primary (legacy method for backward compatibility)
   * @param id Location ID to set as primary
   * @returns Promise with updated location data
   */
  static async setPrimaryLocationLegacy(id: string): Promise<ApiResponse<Location>> {
    try {
      const locationId = parseInt(id);
      const updatedLocation = await this.setPrimaryLocation(locationId);

      // Transform back to legacy format for response
      return {
        data: {
          id: updatedLocation.id?.toString() || '',
          name: updatedLocation.name,
          address_line1: updatedLocation.address.addressLine1,
          address_line2: updatedLocation.address.addressLine2 || null,
          city: updatedLocation.address.city,
          state: updatedLocation.address.state,
          postal_code: updatedLocation.address.postalCode || '',
          country: updatedLocation.address.countryCode,
          is_primary: updatedLocation.isPrimary,
          organization_id: updatedLocation.organizationId.toString(),
          created_by_ip: '***********',
          created_by_user: '<EMAIL>',
          created_date: new Date().toISOString(),
          last_modified_by_ip: '***********',
          last_modified_by_user: '<EMAIL>',
          last_modified_date: new Date().toISOString(),
          version: 1,
          active: true
        },
        message: 'Location set as primary successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error setting location with ID ${id} as primary (legacy):`, error);
      throw error;
    }
  }

  /**
   * Get primary location for an organization
   * @param organizationId Organization ID
   * @returns Promise with primary location data
   */
  static async getPrimaryLocation(organizationId: number): Promise<LocationDTO> {
    try {
      // Use the real API endpoint for primary location
      return await apiClient.get<LocationDTO>(`${this.BASE_URL}/${organizationId}/locations/primary`);
    } catch (error) {
      console.error(`Error fetching primary location for organization ${organizationId}:`, error);
      throw error;
    }
  }
}
