/**
 * Service for handling designation-related API operations
 * Integrated with backend API endpoints
 */

import { apiClient } from './client';
import {
  DesignationDTO,
  Designation,
  CreateDesignationRequest,
  UpdateDesignationRequest,
} from '@/types/designation.types';
import { ApiResponse, PaginatedResponse, QueryParams } from '@/types/api/common';

// Default organization ID for testing/development
const DEFAULT_ORGANIZATION_ID = 40928446087168;

/**
 * Transform DesignationDTO to legacy Designation format for backward compatibility
 * Note: Audit fields are not provided by backend DTO, so they are omitted or set to minimal values
 */
function transformDesignationDTOToLegacy(dto: DesignationDTO): Designation {
  return {
    id: dto.id?.toString() || '',
    name: dto.name,
    description: dto.description || null,
    organization_id: dto.organizationId.toString(),
    // Minimal audit fields - no dummy data
    created_by_ip: '',
    created_by_user: '',
    created_date: '',
    last_modified_by_ip: '',
    last_modified_by_user: '',
    last_modified_date: '',
    version: 0,
    active: true
  };
}

/**
 * Transform form data to DesignationDTO format
 */
function transformFormDataToDesignationDTO(data: any, organizationId: number = DEFAULT_ORGANIZATION_ID): DesignationDTO {
  return {
    name: data.name,
    description: data.description || undefined,
    organizationId: organizationId
  };
}

/**
 * Service class for designation management operations
 */
export class DesignationService {
  private static BASE_URL = '/v1/organization';

  /**
   * Get all designations or by organization ID
   * @param organizationId Optional organization ID to filter designations
   * @returns Promise with designation data
   */
  static async getDesignations(params?: QueryParams): Promise<PaginatedResponse<Designation>> {
    try {
      // Use the default organization ID for testing
      const organizationId = DEFAULT_ORGANIZATION_ID;
      const designationDTOs = await apiClient.get<DesignationDTO[]>(`${this.BASE_URL}/${organizationId}/designations`);

      // Transform DTOs to legacy format for backward compatibility
      const designations = designationDTOs.map(transformDesignationDTOToLegacy);

      // Apply client-side sorting if provided
      let sortedDesignations = [...designations];
      if (params?.sort) {
        const [field, order] = params.sort.split(',');
        sortedDesignations.sort((a: any, b: any) => {
          if (order === 'asc') {
            return a[field] > b[field] ? 1 : -1;
          } else {
            return a[field] < b[field] ? 1 : -1;
          }
        });
      } else {
        // Default sort by name
        sortedDesignations.sort((a, b) => a.name.localeCompare(b.name));
      }

      // Apply pagination
      const page = params?.page || 1;
      const limit = params?.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedDesignations = sortedDesignations.slice(startIndex, endIndex);

      return {
        data: paginatedDesignations,
        total: sortedDesignations.length,
        page,
        limit,
        totalPages: Math.ceil(sortedDesignations.length / limit)
      };
    } catch (error) {
      console.error('Error fetching designations:', error);
      throw error;
    }
  }

  /**
   * Get a designation by ID
   * @param id Designation ID
   * @returns Promise with designation data
   */
  static async getDesignationById(id: string, organizationId: number): Promise<ApiResponse<Designation>> {
    try {
      const designationDTO = await apiClient.get<DesignationDTO>(`${this.BASE_URL}/${organizationId}/designations/${id}`);
      const designation = transformDesignationDTOToLegacy(designationDTO);

      return {
        data: designation,
        message: 'Designation retrieved successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error fetching designation with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new designation
   * @param data Designation data (form data format)
   * @param organizationId Organization ID for the designation
   * @returns Promise with created designation data
   */
  static async createDesignation(data: any, organizationId: number = DEFAULT_ORGANIZATION_ID): Promise<ApiResponse<Designation>> {
    try {
      const designationData = transformFormDataToDesignationDTO(data, organizationId);
      const createdDesignationDTO = await apiClient.post<DesignationDTO>(`${this.BASE_URL}/${organizationId}/designations`, designationData);
      const designation = transformDesignationDTOToLegacy(createdDesignationDTO);

      return {
        data: designation,
        message: 'Designation created successfully',
        status: 201
      };
    } catch (error) {
      console.error('Error creating designation:', error);
      throw error;
    }
  }

  /**
   * Update an existing designation
   * @param id Designation ID
   * @param data Updated designation data (form data format)
   * @returns Promise with updated designation data
   */
  static async updateDesignation(id: string, data: any, organizationId: number): Promise<ApiResponse<Designation>> {
    try {
      const designationData = transformFormDataToDesignationDTO(data, organizationId);
      const updatedDesignationDTO = await apiClient.put<DesignationDTO>(`${this.BASE_URL}/${organizationId}/designations/${id}`, designationData);
      const designation = transformDesignationDTOToLegacy(updatedDesignationDTO);

      return {
        data: designation,
        message: 'Designation updated successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error updating designation with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a designation
   * @param id Designation ID
   * @returns Promise with success message
   */
  static async deleteDesignation(id: string, organizationId: number): Promise<ApiResponse<void>> {
    try {
      await apiClient.delete<void>(`${this.BASE_URL}/${organizationId}/designations/${id}`);

      return {
        data: undefined,
        message: 'Designation deleted successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error deleting designation with ID ${id}:`, error);
      throw error;
    }
  }
}
