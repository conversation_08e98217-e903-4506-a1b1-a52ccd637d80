import { useState } from 'react';
import { useF<PERSON><PERSON>ontex<PERSON>, Controller, RegisterOptions } from 'react-hook-form';
import { AutoComplete } from 'primereact/autocomplete';
import { UserService } from '@/services/api/userService';
import { UserResponse } from '@/types/api/user';

interface RHFAutoCompleteProps {
  name: string;
  label?: string;
  placeholder?: string;
  icon?: string;
  disabled?: boolean;
  className?: string;
  rules?: RegisterOptions;
  excludeUserId?: number; // User ID to exclude from search results
}

interface UserOption {
  label: string;
  value: number;
  email: string;
}

export const RHFAutoComplete = ({
  name,
  label,
  rules,
  placeholder,
  icon,
  excludeUserId,
  ...rest
}: RHFAutoCompleteProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const [suggestions, setSuggestions] = useState<UserOption[]>([]);
  const [inputValue, setInputValue] = useState<string>('');

  const error = errors[name]?.message as string | undefined;

  // Search for users
  const searchUsers = async (query: string) => {
    if (!query || query.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      const users: UserResponse[] = await UserService.searchUsers(query, undefined, undefined, { organizationId: 40928446087168 });

      // Filter out the excluded user (current user in edit mode)
      const filteredUsers = excludeUserId
        ? users.filter(user => user.id !== excludeUserId)
        : users;

      const userOptions: UserOption[] = filteredUsers.map(user => ({
        label: `${user.name} (${user.email})`,
        value: user.id,
        email: user.email
      }));
      setSuggestions(userOptions);
    } catch (error) {
      console.error('Error searching users:', error);
      setSuggestions([]);
    }
  };

  // Handle search input
  const handleSearch = (event: { query: string }) => {
    searchUsers(event.query);
  };

  // Custom item template for dropdown
  const itemTemplate = (item: UserOption) => {
    return (
      <div className="flex align-items-center">
        <i className="pi pi-user mr-2"></i>
        <div>
          <div className="font-medium">{item.label.split(' (')[0]}</div>
          <div className="text-sm text-color-secondary">{item.email}</div>
        </div>
      </div>
    );
  };

  return (
    <div className="field mb-3">
      {label && <label htmlFor={name} className="block mb-1">{label}</label>}
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => {
          // Get the current selected user for display
          const selectedUser = field.value ? suggestions.find(s => s.value === field.value) : null;



          return (
            <div className={`p-inputgroup ${error ? 'p-invalid' : ''}`}>
              {icon && <span className="p-inputgroup-addon"><i className={`pi ${icon}`}></i></span>}
              <AutoComplete
                id={name}
                value={selectedUser ? selectedUser.label : inputValue}
                suggestions={suggestions}
                completeMethod={handleSearch}
                onChange={(e) => {
                  // Handle both selection and typing
                  if (e.value && typeof e.value === 'object' && 'value' in e.value) {
                    // User selected an option from the dropdown
                    field.onChange(e.value.value);
                    setInputValue(''); // Clear input since we have a selection
                  } else {
                    // User is typing or cleared the field
                    const stringValue = typeof e.value === 'string' ? e.value : '';
                    setInputValue(stringValue);

                    // Only clear the form field if input is completely empty
                    if (!stringValue.trim()) {
                      field.onChange(null);
                    }
                    // Don't update field.onChange while typing - only on selection
                  }
                }}
                onBlur={field.onBlur}
                placeholder={placeholder || `Search ${label}`}
                className={`w-full ${error ? 'p-invalid' : ''}`}
                inputClassName="w-full"
                itemTemplate={itemTemplate}
                field="label"
                forceSelection={false}
                emptyMessage="No users found"
                disabled={rest.disabled}
                {...rest}
              />
            </div>
          );
        }}
      />
      {error && <small className="p-error">{error}</small>}
    </div>
  );
};
