.criteria-builder {
  width: 100%;
}

.empty-criteria {
  text-align: center;
  padding: 2rem;
  border: 2px dashed var(--surface-border);
  border-radius: 6px;
  background: var(--surface-50);
  margin-bottom: 1rem;
}

.criteria-row {
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  background: var(--surface-section);
}

.criteria-fields {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr auto auto;
  gap: 1rem;
  align-items: end;
  margin-bottom: 1rem;
}

.criteria-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.875rem;
}

.criteria-actions {
  display: flex;
  align-items: end;
}

.criteria-preview {
  padding: 0.75rem;
  background: var(--surface-100);
  border-radius: 4px;
  border-left: 4px solid var(--primary-color);
}

.preview-text {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-color);
}

.preview-text .operator {
  color: var(--primary-color);
  font-weight: 500;
  text-transform: uppercase;
}

.preview-text .logic-connector {
  color: var(--orange-500);
  font-weight: 600;
  text-transform: uppercase;
  padding: 0 0.25rem;
}

.add-criteria-section {
  margin: 1.5rem 0;
  text-align: center;
}

.criteria-error {
  display: block;
  margin-top: 0.5rem;
  color: var(--red-500);
  font-size: 0.875rem;
}

.criteria-summary {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--blue-50);
  border: 1px solid var(--blue-200);
  border-radius: 6px;
}

.summary-title {
  margin: 0 0 1rem 0;
  color: var(--blue-800);
  font-size: 1rem;
  font-weight: 600;
}

.summary-text {
  color: var(--blue-700);
  line-height: 1.6;
  font-size: 0.875rem;
}

.summary-text .logic-word {
  color: var(--orange-600);
  font-weight: 600;
  text-transform: uppercase;
}

/* Responsive design */
@media (max-width: 1024px) {
  .criteria-fields {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .criteria-field:nth-child(3) {
    grid-column: 1 / -1;
  }
  
  .criteria-actions {
    grid-column: 1 / -1;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .criteria-fields {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .criteria-field {
    grid-column: 1;
  }
  
  .criteria-actions {
    grid-column: 1;
    justify-content: center;
  }
  
  .criteria-row {
    padding: 1rem;
  }
  
  .criteria-summary {
    padding: 1rem;
  }
}

/* Button styling overrides */
.criteria-builder .p-button-outlined {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.criteria-builder .p-button-outlined:hover {
  background: var(--primary-color);
  color: var(--primary-color-text);
}

.criteria-builder .p-button-danger.p-button-text {
  color: var(--red-500);
}

.criteria-builder .p-button-danger.p-button-text:hover {
  background: var(--red-50);
  color: var(--red-600);
}
