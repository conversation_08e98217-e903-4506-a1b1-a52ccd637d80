import React from 'react';
import { WorkflowCriteria } from '@/types/approvalWorkflow.types';
import {
  fieldTypeOptions,
  mockDepartments,
  mockDesignations,
  mockLocations,
  mockEmployees
} from '@/data/mockApprovalData';
import './WorkflowPreview.css';

interface WorkflowPreviewProps {
  criteria: WorkflowCriteria[];
  approvalLevels: number;
  autoApprove: boolean;
  autoReject: boolean;
}

const WorkflowPreview: React.FC<WorkflowPreviewProps> = ({
  criteria,
  approvalLevels,
  autoApprove,
  autoReject
}) => {
  // Get display text for criteria value
  const getValueDisplayText = (criterion: WorkflowCriteria): string => {
    let options;
    switch (criterion.fieldType) {
      case 'department':
        options = mockDepartments;
        break;
      case 'designation':
        options = mockDesignations;
        break;
      case 'location':
        options = mockLocations;
        break;
      case 'employee':
        options = mockEmployees;
        break;
      default:
        return criterion.value;
    }

    const option = options.find(opt => opt.value === criterion.value);
    return option ? option.label : criterion.value;
  };

  // Generate criteria description
  const getCriteriaDescription = (): string => {
    if (criteria.length === 0) {
      return 'No criteria defined - workflow will not be triggered automatically.';
    }

    return criteria.map((criterion, index) => {
      const fieldName = fieldTypeOptions.find(f => f.value === criterion.fieldType)?.label || criterion.fieldType;
      const operator = criterion.operator.replace('_', ' ').toLowerCase();
      const value = getValueDisplayText(criterion);

      let description = `${fieldName} ${operator} "${value}"`;

      if (criterion.logicConnector && index < criteria.length - 1) {
        description += ` ${criterion.logicConnector.toLowerCase()}`;
      }

      return description;
    }).join(' ');
  };

  // Generate approval flow description
  const getApprovalFlowDescription = (): string[] => {
    if (autoApprove) {
      return ['Request automatically approved when criteria match'];
    }

    if (autoReject) {
      return ['Request automatically rejected when criteria match'];
    }

    const flow = ['Request submitted by employee'];

    for (let i = 1; i <= approvalLevels; i++) {
      if (i === 1) {
        flow.push('Sent to direct reporting manager for approval');
      } else {
        flow.push(`Escalated to Level ${i} manager in hierarchy`);
      }
    }

    flow.push('Final decision communicated to employee');
    return flow;
  };

  // Get workflow status
  const getWorkflowStatus = () => {
    if (criteria.length === 0) {
      return {
        type: 'warning',
        message: 'Incomplete - No criteria defined',
        icon: 'pi-exclamation-triangle'
      };
    }

    if (autoApprove) {
      return {
        type: 'success',
        message: 'Auto-approval enabled',
        icon: 'pi-check-circle'
      };
    }

    if (autoReject) {
      return {
        type: 'danger',
        message: 'Auto-rejection enabled',
        icon: 'pi-times-circle'
      };
    }

    return {
      type: 'info',
      message: `${approvalLevels}-level manual approval`,
      icon: 'pi-users'
    };
  };

  const status = getWorkflowStatus();

  return (
    <div className="workflow-preview">
      {/* Status Badge */}
      {/* <div className={`status-badge status-${status.type}`}>
        <i className={`pi ${status.icon}`}></i>
        <span>{status.message}</span>
      </div> */}

      {/* Trigger Conditions */}
      {/* <div className="preview-section">
        <h4 className="section-title">
          <i className="pi pi-filter"></i>
          Trigger Conditions
        </h4>
        <div className="section-content">
          {criteria.length > 0 ? (
            <div className="criteria-display">
              <p className="criteria-text">
                This workflow will be triggered when: <strong>{getCriteriaDescription()}</strong>
              </p>
            </div>
          ) : (
            <div className="empty-state">
              <i className="pi pi-info-circle"></i>
              <span>No criteria defined. Add criteria to specify when this workflow should trigger.</span>
            </div>
          )}
        </div>
      </div> */}

      {/* Approval Flow */}
      {/* <div className="preview-section">
        <h4 className="section-title">
          <i className="pi pi-sitemap"></i>
          Approval Flow
        </h4>
        <div className="section-content">
          <div className="approval-flow">
            {getApprovalFlowDescription().map((step, index) => (
              <div key={index} className="flow-step">
                <div className="step-indicator">
                  <span className="step-number">{index + 1}</span>
                </div>
                <div className="step-content">
                  <span className="step-text">{step}</span>
                </div>
                {index < getApprovalFlowDescription().length - 1 && (
                  <div className="step-arrow">
                    <i className="pi pi-arrow-down"></i>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div> */}

      {/* Summary */}
      <div className="preview-section">
        <h4 className="section-title">
          <i className="pi pi-list"></i>
          Workflow Summary
        </h4>
        <div className="section-content">
          <div className="summary-grid">
            <div className="summary-item">
              <span className="summary-label">Criteria Count:</span>
              <span className="summary-value">{criteria.length} condition{criteria.length !== 1 ? 's' : ''}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Approval Levels:</span>
              <span className="summary-value">
                {autoApprove || autoReject ? 'Automatic' : `${approvalLevels} level${approvalLevels !== 1 ? 's' : ''}`}
              </span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Processing Type:</span>
              <span className="summary-value">
                {autoApprove ? 'Auto-approve' : autoReject ? 'Auto-reject' : 'Manual approval'}
              </span>
            </div>
            {/* <div className="summary-item">
              <span className="summary-label">Expected Duration:</span>
              <span className="summary-value">
                {autoApprove || autoReject ? 'Instant' : `${approvalLevels * 1}-${approvalLevels * 2} business days`}
              </span>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowPreview;
