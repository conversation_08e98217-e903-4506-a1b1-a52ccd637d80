import React from 'react';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { WorkflowCriteria, CriteriaFieldType } from '@/types/approvalWorkflow.types';
import {
  fieldTypeOptions,
  operatorOptions,
  logicConnectorOptions,
  mockDepartments,
  mockDesignations,
  mockLocations,
  mockEmployees
} from '@/data/mockApprovalData';
import './CriteriaBuilder.css';

interface CriteriaBuilderProps {
  criteria: WorkflowCriteria[];
  onChange: (criteria: WorkflowCriteria[]) => void;
  error?: string;
}

const CriteriaBuilder: React.FC<CriteriaBuilderProps> = ({
  criteria,
  onChange,
  error
}) => {
  // Add new criteria row
  const addCriteria = () => {
    const newCriteria: WorkflowCriteria = {
      id: `criteria-${Date.now()}`,
      fieldType: 'department',
      operator: 'IS',
      value: '',
      logicConnector: criteria.length > 0 ? 'AND' : undefined
    };

    onChange([...criteria, newCriteria]);
  };

  // Remove criteria row
  const removeCriteria = (id: string) => {
    const updatedCriteria = criteria.filter(c => c.id !== id);

    // Remove logic connector from the last item if it exists
    if (updatedCriteria.length > 0) {
      updatedCriteria[updatedCriteria.length - 1].logicConnector = undefined;
    }

    onChange(updatedCriteria);
  };

  // Update criteria field
  const updateCriteria = (id: string, field: keyof WorkflowCriteria, value: any) => {
    const updatedCriteria = criteria.map(c => {
      if (c.id === id) {
        const updated = { ...c, [field]: value };

        // Reset value when field type changes
        if (field === 'fieldType') {
          updated.value = '';
        }

        return updated;
      }
      return c;
    });

    onChange(updatedCriteria);
  };

  // Get options for value dropdown based on field type
  const getValueOptions = (fieldType: CriteriaFieldType) => {
    switch (fieldType) {
      case 'department':
        return mockDepartments;
      case 'designation':
        return mockDesignations;
      case 'location':
        return mockLocations;
      case 'employee':
        return mockEmployees;
      default:
        return [];
    }
  };

  // Get display text for selected value
  const getValueDisplayText = (fieldType: CriteriaFieldType, value: string) => {
    const options = getValueOptions(fieldType);
    const option = options.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  return (
    <div className="criteria-builder">
      {criteria.length === 0 && (
        <div className="empty-criteria">
          <p className="text-center text-600 mb-3">
            No criteria defined. Click "Add Criteria" to get started.
          </p>
        </div>
      )}

      {criteria.map((criterion, index) => (
        <div key={criterion.id} className="criteria-row">
          <div className="criteria-fields">
            {/* Field Type Dropdown */}
            <div className="criteria-field">
              <label className="field-label">Field</label>
              <Dropdown
                value={criterion.fieldType}
                options={fieldTypeOptions}
                onChange={(e) => updateCriteria(criterion.id, 'fieldType', e.value)}
                placeholder="Select field"
                className="w-full"
              />
            </div>

            {/* Operator Dropdown */}
            <div className="criteria-field">
              <label className="field-label">Operator</label>
              <Dropdown
                value={criterion.operator}
                options={operatorOptions}
                onChange={(e) => updateCriteria(criterion.id, 'operator', e.value)}
                placeholder="Select operator"
                className="w-full"
              />
            </div>

            {/* Value Dropdown */}
            <div className="criteria-field">
              <label className="field-label">Value</label>
              <Dropdown
                value={criterion.value}
                options={getValueOptions(criterion.fieldType)}
                onChange={(e) => updateCriteria(criterion.id, 'value', e.value)}
                placeholder="Select value"
                className="w-full"
                filter
                emptyMessage="No options available"
              />
            </div>

            {/* Logic Connector (for all except last) */}
            {index < criteria.length - 1 && (
              <div className="criteria-field">
                <label className="field-label">Logic</label>
                <Dropdown
                  value={criterion.logicConnector}
                  options={logicConnectorOptions}
                  onChange={(e) => updateCriteria(criterion.id, 'logicConnector', e.value)}
                  placeholder="AND/OR"
                  className="w-full"
                />
              </div>
            )}

            {/* Remove Button */}
            <div className="criteria-actions">
              <Button
                icon="pi pi-trash"
                className="p-button-danger p-button-text"
                onClick={() => removeCriteria(criterion.id)}
                tooltip="Remove criteria"
                tooltipOptions={{ position: 'top' }}
              />
            </div>
          </div>

          {/* Criteria Preview */}
          <div className="criteria-preview">
            <span className="preview-text">
              <strong>{fieldTypeOptions.find(f => f.value === criterion.fieldType)?.label}</strong>
              {' '}
              <span className="operator">{criterion.operator.replace('_', ' ')}</span>
              {' '}
              <strong>{getValueDisplayText(criterion.fieldType, criterion.value) || '[Select Value]'}</strong>
              {criterion.logicConnector && index < criteria.length - 1 && (
                <>
                  {' '}
                  <span className="logic-connector">{criterion.logicConnector}</span>
                </>
              )}
            </span>
          </div>
        </div>
      ))}

      {/* Add Criteria Button */}
      <div className="add-criteria-section">
        <Button
          icon="pi pi-plus"
          label="Add Criteria"
          className="p-button-outlined"
          onClick={addCriteria}
        />
      </div>

      {/* Error Message */}
      {error && (
        <small className="p-error criteria-error">{error}</small>
      )}

      {/* Criteria Summary */}
      {criteria.length > 0 && (
        <div className="criteria-summary">
          <h4 className="summary-title">Criteria Summary:</h4>
          <div className="summary-text">
            The workflow will be triggered when{' '}
            {criteria.map((criterion, index) => (
              <span key={criterion.id}>
                <strong>{fieldTypeOptions.find(f => f.value === criterion.fieldType)?.label}</strong>
                {' '}
                <em>{criterion.operator.replace('_', ' ').toLowerCase()}</em>
                {' '}
                <strong>"{getValueDisplayText(criterion.fieldType, criterion.value) || '[Select Value]'}"</strong>
                {criterion.logicConnector && index < criteria.length - 1 && (
                  <>
                    {' '}
                    <span className="logic-word">{criterion.logicConnector.toLowerCase()}</span>
                    {' '}
                  </>
                )}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CriteriaBuilder;
