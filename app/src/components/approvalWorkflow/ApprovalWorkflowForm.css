.approval-workflow-form {
  max-width: 100%;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.section-description {
  color: var(--text-color-secondary);
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.875rem;
}

.required {
  color: var(--red-500);
}

.auto-actions-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.field-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.field-checkbox label {
  font-weight: 500;
  color: var(--text-color);
  cursor: pointer;
}

/* Action Type Selection Styles */
.action-type-selection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.action-option {
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  padding: 1.25rem;
  transition: all 0.2s ease;
}

.action-option:hover {
  border-color: var(--primary-color);
  background-color: var(--surface-hover);
}

.action-radio {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.action-label {
  font-weight: 600;
  color: var(--text-color);
  cursor: pointer;
  font-size: 1rem;
}

.action-description {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
  margin-left: 2rem;
  line-height: 1.4;
}

.approval-levels-container {
  margin-top: 1.5rem;
  margin-left: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--surface-border);
}

/* Responsive design */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 0.75rem;
  }

  .form-actions button {
    width: 100%;
  }

  /* Action type selection responsive */
  .action-description {
    margin-left: 1.5rem;
  }

  .approval-levels-container {
    margin-left: 1.5rem;
  }
}

/* Error states */
.p-invalid {
  border-color: var(--red-500) !important;
}

.p-error {
  color: var(--red-500);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
}

/* Divider styling */
.p-divider {
  margin: 1.5rem 0;
}

.p-divider .p-divider-content {
  background: var(--surface-section);
}
