@import url('../../../variables.css');

.edit-approval-workflow .card {
    background: var(--surface-card);
    border-radius: var(--border-radius);
    padding: var(--card-padding-screen);
    box-shadow: var(--card-shadow);
}

.edit-approval-workflow .card--elevated {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Error state styling */
.edit-approval-workflow .text-center {
    text-align: center;
}

.edit-approval-workflow .py-4 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.edit-approval-workflow .mb-3 {
    margin-bottom: 1rem;
}

.edit-approval-workflow .mb-4 {
    margin-bottom: 1.5rem;
}

/* Loading state styling */
.edit-approval-workflow .flex {
    display: flex;
}

.edit-approval-workflow .justify-content-center {
    justify-content: center;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .edit-approval-workflow .card {
        padding: var(--card-padding-mobile);
    }
    
    .edit-approval-workflow .max-w-5xl {
        max-width: 100%;
        margin: 0;
    }
}
