import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useParams } from '@tanstack/react-router';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import ApprovalWorkflowForm from '@/components/approvalWorkflow/ApprovalWorkflowForm';
import { ApprovalWorkflow, UpdateApprovalWorkflowRequest } from '@/types/approvalWorkflow.types';
import { mockWorkflows } from '@/data/mockApprovalData';
import { approvalWorkflowsRoute } from '@/routes/private/approvalWorkflows.route';
import './EditApprovalWorkflow.css';

const EditApprovalWorkflow: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams({ strict: false });
  const toast = useRef<ToastRef>(null);
  const [loading, setLoading] = useState(false);
  const [workflow, setWorkflow] = useState<ApprovalWorkflow | null>(null);
  const [notFound, setNotFound] = useState(false);

  // Load workflow data on component mount
  useEffect(() => {
    const loadWorkflow = async () => {
      try {
        // Simulate API call to fetch workflow by ID
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Find workflow in mock data
        const foundWorkflow = mockWorkflows.find(w => w.id === id);
        
        if (foundWorkflow) {
          setWorkflow(foundWorkflow);
        } else {
          setNotFound(true);
          toast.current?.show({
            severity: 'error',
            summary: 'Error',
            detail: 'Approval workflow not found',
            life: 3000
          });
        }
      } catch (error) {
        console.error('Error loading workflow:', error);
        toast.current?.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load approval workflow',
          life: 3000
        });
        setNotFound(true);
      }
    };

    if (id) {
      loadWorkflow();
    } else {
      setNotFound(true);
    }
  }, [id]);

  // Handle form submission
  const handleSubmit = async (workflowData: UpdateApprovalWorkflowRequest) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success toast and navigate back
      toast.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Approval workflow updated successfully',
        life: 3000
      });

      // Navigate back to workflows list after a short delay
      setTimeout(() => {
        navigate({ to: approvalWorkflowsRoute.to });
      }, 1500);
    } catch (error) {
      console.error('Error updating workflow:', error);
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update approval workflow',
        life: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: approvalWorkflowsRoute.to });
  };

  // Show error state if workflow not found
  if (notFound) {
    return (
      <div className="edit-approval-workflow p-4">
        <Toast ref={toast} position="top-right" />
        <Card
          title="Workflow Not Found"
          subtitle="The requested approval workflow could not be found"
          variant="elevated"
          padding="large"
          className="max-w-3xl mx-auto text-center"
        >
          <div className="py-4">
            <i className="pi pi-exclamation-triangle text-orange-500 text-4xl mb-3"></i>
            <p className="text-600 mb-4">
              The approval workflow you're looking for doesn't exist or may have been deleted.
            </p>
            <button
              className="p-button p-button-primary"
              onClick={handleCancel}
            >
              Back to Workflows
            </button>
          </div>
        </Card>
      </div>
    );
  }

  // Show loading state while fetching workflow
  if (!workflow) {
    return (
      <div className="edit-approval-workflow p-4">
        <Toast ref={toast} position="top-right" />
        <Card
          title="Loading..."
          subtitle="Loading approval workflow details"
          variant="elevated"
          padding="large"
          className="max-w-5xl mx-auto"
        >
          <div className="flex justify-content-center py-4">
            <i className="pi pi-spin pi-spinner text-2xl text-primary"></i>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="edit-approval-workflow p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Edit Approval Workflow"
        subtitle={`Update workflow: ${workflow.name}`}
        variant="elevated"
        padding="large"
        className="max-w-5xl mx-auto"
      >
        <ApprovalWorkflowForm
          workflow={workflow}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={loading}
        />
      </Card>
    </div>
  );
};

export default EditApprovalWorkflow;
