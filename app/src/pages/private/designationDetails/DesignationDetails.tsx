import React, { useState, useRef, useEffect } from 'react';
import { useCreateDesignation, useUpdateDesignation, useDeleteDesignation } from '@/hooks/useDesignation';
import { Designation } from '@/types/designation.types';
import { ColumnConfig, DataGrid } from '@/components/ui/DataGrid/DataGrid';
import { DataTableStateEvent } from 'primereact/datatable';
import Card from '@/components/ui/Card/Card';
import Button from '@/components/ui/Button/Button';
import Modal from '@/components/ui/Modal/Modal';
import { DynamicForm } from '@/components/form/DynamicForm';
import designationFormSchemaJson from '@/formSchemas/designationForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import '@pages/private/designationDetails/DesignationDetails.css';

const designationFormSchema = designationFormSchemaJson as FormSchema;

const DesignationDetails: React.FC = () => {
  // State for designations data - using client-side sorting approach like locations
  const [allDesignations, setAllDesignations] = useState<Designation[]>([]); // All designations from API
  const [designations, setDesignations] = useState<Designation[]>([]); // Displayed designations (sorted & paginated)
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [sortField, setSortField] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<number>(1); // 1 for ascending, -1 for descending

  // State for modal and form
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentDesignation, setCurrentDesignation] = useState<Designation | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  // Toast reference
  const toast = useRef<ToastRef>(null);

  // Mutations
  const createDesignationMutation = useCreateDesignation();
  const updateDesignationMutation = useUpdateDesignation();
  const deleteDesignationMutation = useDeleteDesignation();

  // Load designations on component mount
  useEffect(() => {
    fetchDesignations();
  }, []);

  // Apply sorting and pagination when data or sort/page parameters change
  useEffect(() => {
    if (allDesignations.length > 0) {
      applySortingAndPagination();
    }
  }, [allDesignations, sortField, sortOrder, currentPage, pageSize]);

  // Fetch designations from API (only called once)
  const fetchDesignations = async () => {
    setLoading(true);
    try {
      // Import DesignationService to call directly
      const { DesignationService } = await import('@/services/api/designationService');

      // Get all designations without pagination/sorting (let frontend handle it)
      const designationData = await DesignationService.getDesignations();
      if (designationData?.data) {
        setAllDesignations(designationData.data);
        setTotalRecords(designationData.data.length);
      }
    } catch (error) {
      console.error('Error fetching designations:', error);
      toast.current?.showError('Failed to load designations');
    } finally {
      setLoading(false);
    }
  };

  // Apply sorting and pagination to existing data (no API call)
  const applySortingAndPagination = () => {
    let sortedDesignations = [...allDesignations];

    // Apply sorting
    if (sortField) {
      const sortOrderMultiplier = sortOrder === 1 ? 1 : -1;

      sortedDesignations.sort((a: any, b: any) => {
        let aValue = a[sortField];
        let bValue = b[sortField];

        // Handle null/undefined values
        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return 1;
        if (bValue == null) return -1;

        // Handle string values (case-insensitive)
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
          return sortOrderMultiplier * comparison;
        }

        // Default comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        const comparison = aStr.localeCompare(bStr);
        return sortOrderMultiplier * comparison;
      });
    } else {
      // Default sort by name ascending
      sortedDesignations.sort((a: any, b: any) => {
        const aName = (a.name || '').toLowerCase();
        const bName = (b.name || '').toLowerCase();
        return aName.localeCompare(bName);
      });
    }

    // Apply pagination
    const startIndex = currentPage * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = sortedDesignations.slice(startIndex, endIndex);

    setDesignations(paginatedData);
  };

  // Column definitions for the data grid
  const columns: ColumnConfig[] = [
    {
      field: 'name',
      header: 'Name',
      sortable: true,
    },
    {
      field: 'description',
      header: 'Description',
      sortable: true,
      body: (rowData: Designation) => (
        <div className="line-clamp-2" title={rowData.description || ''}>
          {rowData.description || 'No description'}
        </div>
      ),
    },
    {
      field: 'actions',
      header: 'Actions',
      body: (rowData: Designation) => (
        <div className="flex gap-2">
          <Button
            icon="pi pi-pencil"
            variant="outline"
            size="small"
            onClick={() => handleEdit(rowData)}
          />
          <Button
            icon="pi pi-trash"
            variant="outline"
            size="small"
            onClick={() => handleDelete(rowData)}
          />
        </div>
      ),
    },
  ];

  // Handle page change
  const handlePageChange = (event: { first: number; rows: number; page: number }) => {
    setCurrentPage(event.page);
    setPageSize(event.rows);
  };

  // Handle sort change (no API call, just updates state)
  const handleSort = (event: DataTableStateEvent) => {
    const field = event.sortField || 'name';
    const order = event.sortOrder || 1;

    setSortField(field);
    setSortOrder(order);
  };

  // Handlers for CRUD operations
  const handleAdd = () => {
    setCurrentDesignation(null);
    setFormError(null);
    setIsFormModalOpen(true);
  };

  const handleEdit = (designation: Designation) => {
    setCurrentDesignation(designation);
    setFormError(null);
    setIsFormModalOpen(true);
  };

  const handleDelete = (designation: Designation) => {
    setCurrentDesignation(designation);
    setIsDeleteModalOpen(true);
  };

  const handleFormSubmit = async (formData: any) => {
    try {
      setFormError(null);

      if (currentDesignation) {
        // Update existing designation
        const organizationId = 40928446087168; // Use specified organization ID
        await updateDesignationMutation.mutateAsync({
          id: currentDesignation.id,
          data: formData,
          organizationId: organizationId
        });
        toast.current?.showSuccess('Designation updated successfully');
      } else {
        // Create new designation
        const organizationId = 40928446087168; // Use specified organization ID
        await createDesignationMutation.mutateAsync({ formData, organizationId });
        toast.current?.showSuccess('Designation created successfully');
      }

      // Refresh the data
      await fetchDesignations();

      // Close the modal
      setIsFormModalOpen(false);
    } catch (error) {
      console.error('Error saving designation:', error);
      setFormError('Failed to save designation. Please try again.');
    }
  };

  const handleDeleteConfirm = async () => {
    if (!currentDesignation) return;

    try {
      const designationId = currentDesignation.id;
      const organizationId = 40928446087168; // Use specified organization ID
      await deleteDesignationMutation.mutateAsync({ id: designationId, organizationId: organizationId });
      toast.current?.showSuccess('Designation deleted successfully');

      // Refresh the data
      await fetchDesignations();

      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting designation:', error);
      toast.current?.showError('Failed to delete designation. Please try again.');
    }
  };

  return (
    <div className="designation_details p-4">
      <Toast ref={toast} position="top-right" />
      <Card title="Designation Management" variant="elevated" className="mb-4">
        <div className="flex justify-content-end mb-3">
          <Button
            variant="primary"
            leftIcon={<i className="pi pi-plus"></i>}
            onClick={handleAdd}
          >
            Add Designation
          </Button>
        </div>
        <DataGrid
          value={designations}
          columns={columns}
          totalRecords={totalRecords}
          loading={loading}
          onPage={handlePageChange}
          onSort={handleSort}
          rows={pageSize}
          rowsPerPageOptions={[10, 25, 50]}
          showGridLines={true}
          stripedRows={true}
          sortField={sortField}
          sortOrder={sortOrder}
        />
      </Card>

      {/* Designation Form Modal */}
      <Modal
        visible={isFormModalOpen}
        onHide={() => setIsFormModalOpen(false)}
        header={currentDesignation ? 'Edit Designation' : 'Add Designation'}
        modalProps={{ style: { width: '50vw' } }}
      >
        <DynamicForm
          schema={designationFormSchema}
          onSubmit={handleFormSubmit}
          defaultValues={currentDesignation || {}}
          buttonHandlers={{
            cancel: () => setIsFormModalOpen(false)
          }}
        />
        {formError && <div className="p-error mt-3 text-center">{formError}</div>}
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={isDeleteModalOpen}
        onHide={() => setIsDeleteModalOpen(false)}
        header="Confirm Delete"
        footerButtons={[
          {
            label: 'Cancel',
            onClick: () => setIsDeleteModalOpen(false),
            variant: 'outline'
          },
          {
            label: 'Delete',
            onClick: handleDeleteConfirm,
            variant: 'danger'
          }
        ]}
      >
        <p>Are you sure you want to delete the designation "{currentDesignation?.name}"?</p>
        <p>This action cannot be undone.</p>
      </Modal>
    </div>
  );
};

export default DesignationDetails;
