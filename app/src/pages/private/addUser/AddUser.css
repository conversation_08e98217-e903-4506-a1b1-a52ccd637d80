@import url('../../../variables.css');

.add-user .card {
    background: var(--surface-card);
    border-radius: var(--border-radius);
    padding: var(--card-padding-screen);
    box-shadow: var(--card-shadow);
}

.form-section {
    margin-bottom: 1.5rem;
}

.form-fields-container {
    flex-direction: column;
}

.form-section-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    margin-bottom: 1rem;
    color: var(--text-primary);
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .add-user .card {
        padding: var(--card-padding-mobile);
    }
}
