import React, { useState, useEffect, useRef } from 'react';
import { DataTableStateEvent } from 'primereact/datatable';
import { DataGrid, ColumnConfig } from '@/components/ui/DataGrid/DataGrid';
import Button from '@/components/ui/Button/Button';
import Modal from '@/components/ui/Modal/Modal';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import { DynamicForm } from '@/components/form/DynamicForm';
import locationFormSchemaJson from '@/formSchemas/locationForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import {
  LocationDTO,
  Location,
  CreateLocationRequest,
  UpdateLocationRequest
} from '@/types/location.types';
import { LocationService } from '@/services/api/locationService';
import './LocationDetails.css';

const LocationDetails: React.FC = () => {
  // State for locations data - using display format for compatibility
  const [allLocations, setAllLocations] = useState<any[]>([]); // All locations from API
  const [locations, setLocations] = useState<any[]>([]); // Displayed locations (sorted & paginated)
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [sortField, setSortField] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<number>(1); // 1 for ascending, -1 for descending

  // State for current location - using display format for compatibility
  const [currentLocation, setCurrentLocation] = useState<any | null>(null);

  // Organization ID - using the provided organization ID
  const organizationId = 40928446087168;

  // State for modals
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [isPrimaryModalOpen, setIsPrimaryModalOpen] = useState<boolean>(false);
  const [formError, setFormError] = useState<string>('');

  // Ref for toast notifications
  const toast = useRef<ToastRef>(null);

  // Form schema
  const locationFormSchema = locationFormSchemaJson as FormSchema;

  // Load locations on component mount
  useEffect(() => {
    fetchLocations();
  }, []);

  // Apply sorting and pagination when data or sort/page parameters change
  useEffect(() => {
    if (allLocations.length > 0) {
      applySortingAndPagination();
    }
  }, [allLocations, sortField, sortOrder, currentPage, pageSize]);

  // Fetch locations from API (only called once)
  const fetchLocations = async () => {
    setLoading(true);
    try {
      // Get all locations without pagination/sorting (let frontend handle it)
      const allLocationData = await LocationService.getLocations(organizationId);

      // Transform to display format
      const transformedLocations = allLocationData.map((locationDTO: any) => ({
        id: locationDTO.id?.toString() || '',
        name: locationDTO.name,
        timezone: locationDTO.timezone,
        organizationId: locationDTO.organizationId,
        addressLine1: locationDTO.address.addressLine1,
        addressLine2: locationDTO.address.addressLine2,
        city: locationDTO.address.city,
        state: locationDTO.address.state,
        countryCode: locationDTO.address.countryCode,
        postalCode: locationDTO.address.postalCode,
        latitude: locationDTO.address.latitude,
        longitude: locationDTO.address.longitude,
        primary: locationDTO.primary, // Use 'primary' field from backend
      }));

      setAllLocations(transformedLocations);
      setTotalRecords(transformedLocations.length);
    } catch (error) {
      console.error('Error fetching locations:', error);
      toast.current?.showError('Failed to load locations');
    } finally {
      setLoading(false);
    }
  };

  // Apply sorting and pagination to existing data (no API call)
  const applySortingAndPagination = () => {
    let sortedLocations = [...allLocations];

    // Apply sorting
    if (sortField) {
      const sortOrderMultiplier = sortOrder === 1 ? 1 : -1;

      sortedLocations.sort((a: any, b: any) => {
        let aValue = a[sortField];
        let bValue = b[sortField];

        // Handle null/undefined values
        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return 1;
        if (bValue == null) return -1;

        // Handle boolean values
        if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
          if (aValue === bValue) return 0;
          return sortOrderMultiplier * (aValue ? 1 : -1);
        }

        // Handle string values (case-insensitive)
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
          return sortOrderMultiplier * comparison;
        }

        // Handle numeric values
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortOrderMultiplier * (aValue - bValue);
        }

        // Default comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        const comparison = aStr.localeCompare(bStr);
        return sortOrderMultiplier * comparison;
      });
    } else {
      // Default sort by name ascending
      sortedLocations.sort((a: any, b: any) => {
        const aName = (a.name || '').toLowerCase();
        const bName = (b.name || '').toLowerCase();
        return aName.localeCompare(bName);
      });
    }

    // Apply pagination
    const startIndex = currentPage * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = sortedLocations.slice(startIndex, endIndex);

    setLocations(paginatedData);
  };

  // Handle page change
  const handlePageChange = (event: { first: number; rows: number; page: number }) => {
    setCurrentPage(event.page);
    setPageSize(event.rows);
  };

  // Handle sort change (no API call, just updates state)
  const handleSort = (event: DataTableStateEvent) => {
    const field = event.sortField || 'name';
    const order = event.sortOrder || 1;

    setSortField(field);
    setSortOrder(order);
  };

  // Handle add location
  const handleAdd = () => {
    setCurrentLocation(null);
    setFormError('');
    setIsFormModalOpen(true);
  };

  // Handle edit location
  const handleEdit = (location: Location) => {
    setCurrentLocation(location);
    setFormError('');
    setIsFormModalOpen(true);
  };

  // Handle delete location
  const handleDeleteClick = (location: Location) => {
    setCurrentLocation(location);
    setIsDeleteModalOpen(true);
  };

  // Handle set primary location
  const handleSetPrimaryClick = (location: any) => {
    if (location.primary) {
      toast.current?.showInfo('This location is already set as primary');
      return;
    }
    setCurrentLocation(location);
    setIsPrimaryModalOpen(true);
  };

  // Handle form submission
  const handleFormSubmit = async (data: any) => {
    try {
      // Ensure primary is set correctly
      const formattedData = {
        ...data,
        primary: currentLocation?.primary || false
      };

      if (currentLocation) {
        // Update existing location
        const locationId = parseInt(currentLocation.id);
        await LocationService.updateLocation(locationId, formattedData, organizationId);
        toast.current?.showSuccess('Location updated successfully');
      } else {
        // Create new location
        await LocationService.createLocation(formattedData, organizationId);
        toast.current?.showSuccess('Location added successfully');
      }
      setIsFormModalOpen(false);
      fetchLocations(); // Refresh data from API
    } catch (error) {
      console.error('Error saving location:', error);
      setFormError('Failed to save location. Please try again.');
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!currentLocation) return;

    try {
      const locationId = parseInt(currentLocation.id);
      await LocationService.deleteLocation(locationId, organizationId);
      toast.current?.showSuccess('Location deleted successfully');
      setIsDeleteModalOpen(false);
      fetchLocations(); // Refresh data from API
    } catch (error) {
      console.error('Error deleting location:', error);
      toast.current?.showError('Failed to delete location');
    }
  };

  // Handle set primary confirmation
  const handleSetPrimaryConfirm = async () => {
    if (!currentLocation) return;

    try {
      const locationId = parseInt(currentLocation.id);
      await LocationService.setPrimaryLocation(locationId, organizationId);
      toast.current?.showSuccess('Primary location updated successfully');
      setIsPrimaryModalOpen(false);
      fetchLocations(); // Refresh data from API
    } catch (error) {
      console.error('Error setting primary location:', error);
      toast.current?.showError('Failed to update primary location');
    }
  };

  // Column definitions for the data grid
  const columns: ColumnConfig[] = [
    {
      field: 'name',
      header: 'Location Name',
      sortable: true,
    },
    {
      field: 'timezone',
      header: 'Timezone',
      sortable: true,
    },
    {
      field: 'addressLine1',
      header: 'Address',
      sortable: true,
    },
    {
      field: 'city',
      header: 'City',
      sortable: true,
    },
    {
      field: 'state',
      header: 'State',
      sortable: true,
    },
    {
      field: 'countryCode',
      header: 'Country',
      sortable: true,
    },
    {
      field: 'primary',
      header: 'Primary Location',
      sortable: true,
      body: (rowData: any) => (
        <span className={`status-badge ${rowData.primary ? 'primary' : 'not-primary'}`}>
          {rowData.primary ? 'Primary' : 'Secondary'}
        </span>
      ),
    },
    {
      field: 'actions',
      header: 'Actions',
      body: (rowData: any) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="small"
            leftIcon={<i className="pi pi-pencil"></i>}
            onClick={() => handleEdit(rowData)}
          />

          <Button
            variant="outline"
            size="small"
            leftIcon={<i className="pi pi-trash"></i>}
            onClick={() => handleDeleteClick(rowData)}
          />

          <Button
            variant="outline"
            size="small"
            leftIcon={<i className="pi pi-check-circle"></i>}
            onClick={() => handleSetPrimaryClick(rowData)}
            disabled={rowData.primary}
            className={rowData.primary ? 'primary-button-disabled' : ''}
          />
        </div>
      ),
    },
  ];

  return (
    <div className="location_details p-4">
      <Toast ref={toast} />
      <Card title="Location Management" variant="elevated" className="mb-4">
        <div className="flex justify-content-end mb-3">
          <Button
            variant="primary"
            leftIcon={<i className="pi pi-plus"></i>}
            onClick={handleAdd}
          >
            Add Location
          </Button>
        </div>
        <DataGrid
          value={locations}
          columns={columns}
          totalRecords={totalRecords}
          loading={loading}
          onPage={handlePageChange}
          onSort={handleSort}
          rows={pageSize}
          rowsPerPageOptions={[10, 25, 50]}
          showGridLines={true}
          stripedRows={true}
          sortField={sortField}
          sortOrder={sortOrder}
        />
      </Card>

      {/* Form Modal */}
      <Modal
        visible={isFormModalOpen}
        onHide={() => setIsFormModalOpen(false)}
        header={currentLocation ? 'Edit Location' : 'Add Location'}
        modalProps={{ style: { width: '50vw' } }}
      >
        <DynamicForm
          schema={locationFormSchema}
          onSubmit={handleFormSubmit}
          defaultValues={currentLocation || {}}
          buttonHandlers={{
            cancel: () => setIsFormModalOpen(false)
          }}
        />
        {formError && <div className="p-error mt-3 text-center">{formError}</div>}
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={isDeleteModalOpen}
        onHide={() => setIsDeleteModalOpen(false)}
        header="Confirm Delete"
        footerButtons={[
          {
            label: 'Cancel',
            onClick: () => setIsDeleteModalOpen(false),
            variant: 'outline'
          },
          {
            label: 'Delete',
            onClick: handleDeleteConfirm,
            variant: 'danger'
          }
        ]}
      >
        <p>Are you sure you want to delete the location "{currentLocation?.name}"?</p>
        <p>This action cannot be undone.</p>
        {currentLocation?.primary && (
          <>
            <p className="p-error">Warning: This is a primary location.</p>
            <p>If you delete this location, the system will automatically set another location as primary.</p>
          </>
        )}
      </Modal>

      {/* Set Primary Confirmation Modal */}
      <Modal
        visible={isPrimaryModalOpen}
        onHide={() => {
          setIsPrimaryModalOpen(false);
        }}
        header="Confirm Primary Location"
        footerButtons={[
          {
            label: 'Cancel',
            onClick: () => {
              setIsPrimaryModalOpen(false);
            },
            variant: 'outline'
          },
          {
            label: 'Confirm',
            onClick: handleSetPrimaryConfirm,
            variant: 'primary'
          }
        ]}
      >
        <p>Are you sure you want to set "{currentLocation?.name}" as the primary location?</p>
        <p>This will change the current primary location to secondary.</p>
      </Modal>
    </div>
  );
};

export default LocationDetails;
