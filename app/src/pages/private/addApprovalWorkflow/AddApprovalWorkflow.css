@import url('../../../variables.css');

.add-approval-workflow .card {
    background: var(--surface-card);
    border-radius: var(--border-radius);
    padding: var(--card-padding-screen);
    box-shadow: var(--card-shadow);
}

.add-approval-workflow .card--elevated {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .add-approval-workflow .card {
        padding: var(--card-padding-mobile);
    }
    
    .add-approval-workflow .max-w-5xl {
        max-width: 100%;
        margin: 0;
    }
}
