import React, { useState, useRef } from 'react';
import { useNavigate } from '@tanstack/react-router';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import ApprovalWorkflowForm from '@/components/approvalWorkflow/ApprovalWorkflowForm';
import { CreateApprovalWorkflowRequest } from '@/types/approvalWorkflow.types';
import { approvalWorkflowsRoute } from '@/routes/private/approvalWorkflows.route';
import './AddApprovalWorkflow.css';

const AddApprovalWorkflow: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);
  const [loading, setLoading] = useState(false);

  // Handle form submission
  const handleSubmit = async (workflowData: CreateApprovalWorkflowRequest) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success toast and navigate back
      toast.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Approval workflow created successfully',
        life: 3000
      });

      // Navigate back to workflows list after a short delay
      setTimeout(() => {
        navigate({ to: approvalWorkflowsRoute.to });
      }, 1500);
    } catch (error) {
      console.error('Error creating workflow:', error);
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create approval workflow',
        life: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: approvalWorkflowsRoute.to });
  };

  return (
    <div className="add-approval-workflow p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Add Approval Workflow"
        subtitle="Configure a new approval workflow for your organization"
        variant="elevated"
        padding="large"
        className="max-w-5xl mx-auto"
      >
        <ApprovalWorkflowForm
          workflow={null}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={loading}
        />
      </Card>
    </div>
  );
};

export default AddApprovalWorkflow;
