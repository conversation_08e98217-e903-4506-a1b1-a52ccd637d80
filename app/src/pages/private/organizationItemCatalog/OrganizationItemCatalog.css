@import url('../../../variables.css');

.organization-item-catalog .card {
    background: var(--surface-card);
    border-radius: var(--border-radius);
    padding: var(--card-padding-screen);
    box-shadow: var(--card-shadow);
}

/* Tab styling */
.organization-item-catalog .p-tabview .p-tabview-nav {
    border-bottom: 1px solid var(--border-primary);
}

.organization-item-catalog .p-tabview .p-tabview-nav li .p-tabview-nav-link {
    color: var(--text-secondary);
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
    font-weight: 500;
    padding: 1rem 1.5rem;
}

.organization-item-catalog .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
    color: var(--color-primary);
    border-bottom: 2px solid var(--color-primary);
}

.organization-item-catalog .p-tabview .p-tabview-nav li:not(.p-highlight):not(.p-disabled):hover .p-tabview-nav-link {
    color: var(--color-primary);
    border-bottom: 2px solid var(--color-primary-light);
}

.organization-item-catalog .p-tabview .p-tabview-panels {
    padding: 1.5rem 0 0 0;
}

/* Filter container */
.filter-container {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.category-filter,
.subcategory-filter,
.sort-filter {
    min-width: 200px;
}

.header-container .button{
    height: fit-content;

}

/* Image cell styling */
.image-cell {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-cell img.thumbnail-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
}

.image-placeholder {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--surface-ground);
    border-radius: 4px;
}

.image-placeholder i {
    color: var(--text-secondary);
    font-size: 1.5rem;
}

.image-placeholder i.pi-spinner {
    color: var(--color-primary);
    font-size: 1.2rem;
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-start;
}

/* Product cards */
.product-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.product-card {
    display: flex;
    flex-direction: column;
    background: var(--surface-card);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.product-card-image {
    height: 180px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--surface-ground);
}

.product-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image-placeholder i.pi-spinner {
    color: var(--color-primary);
    font-size: 2rem;
}

.product-card-content {
    padding: 1rem;
}

.product-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.product-card-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}



.product-card-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.product-card-details .label {
    font-weight: 600;
    color: var(--text-primary);
}

.product-card-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-primary);
}

.empty-message {
    padding: 2rem;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
}

/* Loading state for product cards */
.product-cards-loading {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.product-card-skeleton {
    display: flex;
    flex-direction: column;
    background: var(--surface-card);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    animation: pulse 1.5s ease-in-out infinite;
}

.product-card-skeleton .skeleton-image {
    height: 180px;
    background: var(--surface-ground);
}

.product-card-skeleton .skeleton-content {
    padding: 1rem;
}

.product-card-skeleton .skeleton-line {
    height: 1rem;
    background: var(--surface-ground);
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.product-card-skeleton .skeleton-line.title {
    height: 1.5rem;
    width: 70%;
}

.product-card-skeleton .skeleton-line.description {
    width: 90%;
}

.product-card-skeleton .skeleton-line.category {
    width: 60%;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Product paginator */
.product-paginator {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-primary);
}

/* Responsive adjustments */
@media screen and (max-width: 992px) {
    .product-cards-container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media screen and (max-width: 768px) {
    .organization-item-catalog .card {
        padding: var(--card-padding-mobile);
    }

    .filter-container {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .category-filter,
    .subcategory-filter,
    .sort-filter {
        min-width: auto;
        width: 100%;
    }

    .product-cards-container {
        grid-template-columns: 1fr;
    }
}
