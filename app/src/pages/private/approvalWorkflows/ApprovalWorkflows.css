.approval-workflows-page {
  padding: 1rem;
}

.approval-workflows-table {
  margin-top: 1rem;
}

.approval-workflows-table .p-datatable-header {
  background: var(--surface-section);
  border: 1px solid var(--surface-border);
  padding: 1rem;
  border-radius: 6px 6px 0 0;
}

.approval-workflows-table .p-datatable-thead > tr > th {
  background: var(--surface-section);
  color: var(--text-color);
  border: 1px solid var(--surface-border);
  padding: 0.75rem 1rem;
  font-weight: 600;
}

.approval-workflows-table .p-datatable-tbody > tr > td {
  padding: 0.75rem 1rem;
  border: 1px solid var(--surface-border);
}

.approval-workflows-table .p-datatable-tbody > tr:hover {
  background: var(--surface-hover);
}

.confirmation-content {
  display: flex;
  align-items: center;
  padding: 1rem 0;
}

.confirmation-content i {
  flex-shrink: 0;
}

.confirmation-content span {
  line-height: 1.5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .approval-workflows-page {
    padding: 0.5rem;
  }
  
  .approval-workflows-table .p-datatable-header {
    padding: 0.75rem;
  }
  
  .approval-workflows-table .p-datatable-header .flex {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .approval-workflows-table .p-datatable-header .w-20rem {
    width: 100% !important;
  }
}
