import { createRoute } from "@tanstack/react-router";
import { appRoute } from "@/routes/private/app.route";
import { ROUTES } from "@/constants/routes.constant";
import ApprovalWorkflows from "@/pages/private/approvalWorkflows/ApprovalWorkflows";

export const approvalWorkflowsRoute = createRoute({
    getParentRoute: () => appRoute,
    path: ROUTES.PRIVATE.APPROVAL_WORKFLOWS,
    component: ApprovalWorkflows,
    head: () => ({
        meta: [
            {
                title: "Approval Workflows - Avinya Ops",
            },
        ],
    }),
});
