import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import EditUser from "@/pages/private/editUser/EditUser";

export const editUserRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.EDIT_USER,
  component: EditUser,
  validateSearch: (search: Record<string, unknown>) => {
    // Ensure id is always a string and is required
    if (typeof search.id !== 'string' || !search.id) {
      throw new Error('User ID is required and must be a string');
    }
    return {
      id: search.id,
    };
  },
});
