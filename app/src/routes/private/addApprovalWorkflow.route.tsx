import { createRoute } from "@tanstack/react-router";
import { appRoute } from "@/routes/private/app.route";
import { ROUTES } from "@/constants/routes.constant";
import AddApprovalWorkflow from "@/pages/private/addApprovalWorkflow/AddApprovalWorkflow";

export const addApprovalWorkflowRoute = createRoute({
    getParentRoute: () => appRoute,
    path: ROUTES.PRIVATE.APPROVAL_WORKFLOWS + ROUTES.PRIVATE.ADD_APPROVAL_WORKFLOW,
    component: AddApprovalWorkflow,
    head: () => ({
        meta: [
            {
                title: "Add Approval Workflow - Avinya Ops",
            },
        ],
    }),
});
