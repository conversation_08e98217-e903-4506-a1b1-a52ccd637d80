import { createRoute } from "@tanstack/react-router";
import { appRoute } from "@/routes/private/app.route";
import { ROUTES } from "@/constants/routes.constant";
import EditApprovalWorkflow from "@/pages/private/editApprovalWorkflow/EditApprovalWorkflow";

export const editApprovalWorkflowRoute = createRoute({
    getParentRoute: () => appRoute,
    path: ROUTES.PRIVATE.APPROVAL_WORKFLOWS + ROUTES.PRIVATE.EDIT_APPROVAL_WORKFLOW + '/$id',
    component: EditApprovalWorkflow,
    head: () => ({
        meta: [
            {
                title: "Edit Approval Workflow - Avinya Ops",
            },
        ],
    }),
});
