package com.avinyaops.procurement.fileupload;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

@Entity
@Table(name = "file_metadata")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileMetadata {
    @Id
    private UUID id;

    private String fileName;
    private String contentType;
    private Long size;
    private LocalDateTime uploadTime;
    private String storageKey;

    public Map<String, String> toMap() {
        return Map.of(
                "fileName", this.fileName,
                "contentType", this.contentType,
                "size", String.valueOf(this.size),
                "uploadTime", this.uploadTime.toString(),
                "storageKey", this.storageKey);
    }
}