package com.avinyaops.procurement.fileupload;

import com.avinyaops.fileupload.FileStorageService;
import com.avinyaops.fileupload.UploadFileResponseDto;

import lombok.RequiredArgsConstructor;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/v1/files")
@RequiredArgsConstructor
public class FileUploadController {

    private final FileStorageService fileStorageService;

    @PostMapping("/upload")
    public ResponseEntity<UploadFileResponseDto> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("folderPath") String folderPath) {
        return ResponseEntity.ok(fileStorageService.uploadFile(file, folderPath));
    }

    @PostMapping("/upload/batch")
    public ResponseEntity<List<UploadFileResponseDto>> uploadFiles(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam("folderPath") String folderPath) {
        return ResponseEntity.ok(fileStorageService.uploadFiles(files, folderPath));
    }

    @GetMapping("/view/{fileId}")
    public ResponseEntity<String> getViewUrl(@PathVariable String fileId) {
        return ResponseEntity.ok(fileStorageService.generateViewUrl(fileId));
    }

    @DeleteMapping("/delete/{fileId}")
    public ResponseEntity<String> deleteFile(@PathVariable String fileId) {
        return ResponseEntity.ok(fileStorageService.deleteFile(fileId));
    }
}