package com.avinyaops.procurement.fileupload;

import com.avinyaops.procurement.exception.AvinyaException;

public class InvalidFileTypeException extends AvinyaException {
    private static final String ERROR_CODE = "INVALID_FILE_TYPE";

    public InvalidFileTypeException(String fileName, String allowedTypes) {
        super(ERROR_CODE, String.format("File '%s' has invalid type. Allowed types: %s", fileName, allowedTypes));
    }

    public InvalidFileTypeException(String fileName, String allowedTypes, Throwable cause) {
        super(ERROR_CODE, String.format("File '%s' has invalid type. Allowed types: %s", fileName, allowedTypes),
                cause);
    }
}
