package com.avinyaops.procurement.fileupload;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import com.avinyaops.procurement.exception.ErrorResponse;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestControllerAdvice(basePackages = "com.avinyaops.procurement.fileupload")
@Order(Ordered.HIGHEST_PRECEDENCE)
public class GlobalFileUploadExceptionHandler {

    @ExceptionHandler(FileUploadException.class)
    public ResponseEntity<ErrorResponse> handleFileUploadException(FileUploadException fex, WebRequest request) {
        log.error("File Upload Exception: ", fex);
        ErrorResponse errorResponse = ErrorResponse.of(fex.getErrorCode(), fex.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(FileNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleFileNotFoundException(FileNotFoundException fnfex, WebRequest request) {
        log.error("File Not Found Exception: ", fnfex);
        ErrorResponse errorResponse = ErrorResponse.of(fnfex.getErrorCode(), fnfex.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleAllUncaughtException(Exception ex, WebRequest request,
            HttpServletRequest httpRequest) {
        log.error("Unknown error occurred: ", ex);
        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(java.time.Instant.now())
                .errorCode("FILE_UPLOAD_INTERNAL_ERROR")
                .message("An unexpected error occurred in file upload")
                .path(httpRequest.getRequestURI())
                .build();
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}