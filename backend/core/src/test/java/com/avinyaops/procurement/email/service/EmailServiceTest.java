package com.avinyaops.procurement.email.service;

import com.avinyaops.procurement.email.EmailConfig;
import com.avinyaops.procurement.email.EmailRequest;
import com.avinyaops.procurement.email.EmailServiceImpl;
import com.icegreen.greenmail.junit5.GreenMailExtension;
import com.icegreen.greenmail.util.ServerSetupTest;
import jakarta.mail.internet.MimeMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.util.Properties;

import static org.assertj.core.api.Assertions.assertThat;

public class EmailServiceTest {

    @RegisterExtension
    static GreenMailExtension greenMail = new GreenMailExtension(ServerSetupTest.SMTP);

    private EmailServiceImpl emailService;

    @BeforeEach
    void setUp() {
        // JavaMailSender setup
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost("localhost");
        mailSender.setPort(greenMail.getSmtp().getPort());
        
        Properties props = new Properties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "false");
        props.put("mail.smtp.starttls.enable", "false");
        props.put("mail.debug", "false");
        mailSender.setJavaMailProperties(props);

        // Dummy EmailConfig
        EmailConfig emailConfig = new EmailConfig();
        emailConfig.setFrom("<EMAIL>");

        // Minimal template engine
        SpringTemplateEngine templateEngine = new SpringTemplateEngine();

        emailService = new EmailServiceImpl(mailSender, templateEngine, emailConfig);
    }

    @Test
    void testSendSimpleEmail() throws Exception {
        EmailRequest request = EmailRequest.builder()
                .to("<EMAIL>")
                .subject("Hello from GreenMail")
                .templateName("unused") // not used since we don't use template engine here
                .isHtml(false)
                .build();

        emailService.sendEmail(request);

        // Validate email was received
        MimeMessage[] receivedMessages = greenMail.getReceivedMessages();
        assertThat(receivedMessages).hasSize(1);
        assertThat(receivedMessages[0].getSubject()).isEqualTo("Hello from GreenMail");
        assertThat(receivedMessages[0].getAllRecipients()[0].toString()).isEqualTo("<EMAIL>");
    }
}
