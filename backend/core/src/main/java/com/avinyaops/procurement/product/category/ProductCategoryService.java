package com.avinyaops.procurement.product.category;

import java.util.List;

public interface ProductCategoryService {
    ProductCategoryResponseDTO createCategory(ProductCategoryRequestDTO categoryRequestDTO);
    
    ProductCategoryResponseDTO updateCategory(Long id, ProductCategoryRequestDTO categoryRequestDTO);
    
    void deleteCategory(Long id, Long organizationId);
    
    ProductCategoryResponseDTO getCategory(Long id, Long organizationId);
    
    List<ProductCategoryResponseDTO> getAllCategories(Long organizationId);
    
    ProductSubCategoryResponseDTO createSubCategory(Long categoryId, ProductSubCategoryRequestDTO subCategoryDTO);
    
    ProductSubCategoryResponseDTO updateSubCategory(Long categoryId, Long subCategoryId, ProductSubCategoryRequestDTO subCategoryDTO);
    
    void deleteSubCategory(Long categoryId, Long subCategoryId, Long organizationId);
    
    ProductSubCategoryResponseDTO getSubCategory(Long categoryId, Long subCategoryId, Long organizationId);
    
    List<ProductSubCategoryResponseDTO> getAllSubCategoriesByCategoryId(Long categoryId, Long organizationId);
    
    List<ProductSubCategoryResponseDTO> getAllSubCategories(Long organizationId);
} 