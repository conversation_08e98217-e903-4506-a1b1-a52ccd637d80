package com.avinyaops.procurement.organization;

import com.avinyaops.procurement.organization.location.LocationDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrganizationDetailResponse {
    private Long id;
    private String name;
    private String description;
    private boolean enabled;
    private String logoFileId;
    private LocationDTO primaryLocation;
}