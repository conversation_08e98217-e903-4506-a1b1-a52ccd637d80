package com.avinyaops.procurement.organization;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class OrganizationServiceImpl implements OrganizationService {
    private final OrganizationRepository organizationRepository;

    @Override
    public OrganizationDetailResponse createOrganization(OrganizationCreateUpdateRequest request) {
        if (organizationRepository.existsByName(request.getName())) {
            throw new OrganizationAlreadyExistsException(request.getName());
        }

        Organization organization = Organization.builder()
                .name(request.getName())
                .description(request.getDescription())
                .enabled(request.isEnabled())
                .logoFileId(request.getLogoFileId())
                .build();
        organization = organizationRepository.save(organization);
        return toDetailResponse(organization);
    }

    @Override
    public OrganizationDetailResponse updateOrganization(Long id, OrganizationCreateUpdateRequest request) {
        Organization organization = organizationRepository.findById(id)
                .orElseThrow(() -> new OrganizationNotFoundException(id));
        
        organization.setName(request.getName());
        organization.setDescription(request.getDescription());
        organization.setEnabled(request.isEnabled());
        organization.setLogoFileId(request.getLogoFileId());
        
        organization = organizationRepository.save(organization);
        return toDetailResponse(organization);
    }

    @Override
    public void deleteOrganization(Long id) {
        Organization organization = organizationRepository.findById(id)
                .orElseThrow(() -> new OrganizationNotFoundException(id));
        //TODO: add resource in use exception and check, if any department designation or user referes to the org(if needed)
        organization.softDelete();
        organizationRepository.save(organization);
    }

    @Override
    @Transactional(readOnly = true)
    public OrganizationDetailResponse getOrganization(Long id) {
        Organization organization = organizationRepository.findById(id)
                .orElseThrow(() -> new OrganizationNotFoundException(id));
        return toDetailResponse(organization);
    }

    @Override
    @Transactional(readOnly = true)
    public List<OrganizationDetailResponse> getAllOrganizationsDetail() {
        return organizationRepository.findAll().stream()
                .map(this::toDetailResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<OrganizationShortResponse> getAllOrganizationsShort() {
        return organizationRepository.findAll().stream()
                .map(this::toShortResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Organization getOrganizationEntity(Long id) {
        return organizationRepository.findById(id)
                .orElseThrow(() -> new OrganizationNotFoundException(id));
    }

    private OrganizationDetailResponse toDetailResponse(Organization organization) {
        return OrganizationDetailResponse.builder()
                .id(organization.getId())
                .name(organization.getName())
                .description(organization.getDescription())
                .enabled(organization.isEnabled())
                .logoFileId(organization.getLogoFileId())
                .build();
    }

    private OrganizationShortResponse toShortResponse(Organization organization) {
        return OrganizationShortResponse.builder()
                .id(organization.getId())
                .name(organization.getName())
                .enabled(organization.isEnabled())
                .logoFileId(organization.getLogoFileId())
                .build();
    }
}
