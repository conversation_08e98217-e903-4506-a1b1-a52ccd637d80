package com.avinyaops.procurement.product.category;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/v1/organization/{organizationId}/product-categories")
@RequiredArgsConstructor
@Tag(name = "Product Category Management", description = "APIs for managing product categories and sub-categories")
public class ProductCategoryController {
    private final ProductCategoryService categoryService;

    @Operation(summary = "Create a new product category", description = "Creates a new product category. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Category created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "409", description = "Category with same name already exists")
    })
    @PostMapping
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ProductCategoryResponseDTO> createCategory(
            @PathVariable Long organizationId,
            @Valid @ModelAttribute ProductCategoryRequestDTO categoryRequestDTO) {
        return ResponseEntity.ok(categoryService.createCategory(categoryRequestDTO));
    }

    @Operation(summary = "Update an existing product category", description = "Updates an existing product category. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Category updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Category not found"),
        @ApiResponse(responseCode = "409", description = "Category with same name already exists")
    })
    @PutMapping("/{id}")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ProductCategoryResponseDTO> updateCategory(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Category ID") @PathVariable Long id,
            @Valid @ModelAttribute ProductCategoryRequestDTO categoryRequestDTO) {
        return ResponseEntity.ok(categoryService.updateCategory(id, categoryRequestDTO));
    }

    @Operation(summary = "Delete a product category", description = "Soft deletes a product category. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Category deleted successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Category not found")
    })
    @DeleteMapping("/{id}")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteCategory(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Category ID") @PathVariable Long id) {
        categoryService.deleteCategory(id, organizationId);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Get a product category by ID", description = "Retrieves a product category by its ID. Requires authentication.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Category found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Category not found")
    })
    @GetMapping("/{id}")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ProductCategoryResponseDTO> getCategory(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Category ID") @PathVariable Long id) {
        return ResponseEntity.ok(categoryService.getCategory(id, organizationId));
    }

    @Operation(summary = "Get all product categories", description = "Retrieves all product categories. If organizationId is provided in the request body, returns categories for that organization and global categories. Otherwise, returns only global categories.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Categories retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductCategoryResponseDTO>> getAllCategories(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId
    ) {
        return ResponseEntity.ok(categoryService.getAllCategories(organizationId));
    }

    @Operation(summary = "Create a new sub-category", description = "Creates a new sub-category under a product category. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Sub-category created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Parent category not found"),
        @ApiResponse(responseCode = "409", description = "Sub-category with same name already exists")
    })
    @PostMapping("/{categoryId}/sub-categories")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ProductSubCategoryResponseDTO> createSubCategory(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Parent category ID") @PathVariable Long categoryId,
            @Valid @ModelAttribute ProductSubCategoryRequestDTO subCategoryDTO) {
        return ResponseEntity.ok(categoryService.createSubCategory(categoryId, subCategoryDTO));
    }

    @Operation(summary = "Update a sub-category", description = "Updates an existing sub-category. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Sub-category updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Sub-category not found"),
        @ApiResponse(responseCode = "409", description = "Sub-category with same name already exists")
    })
    @PutMapping("/{categoryId}/sub-categories/{subCategoryId}")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ProductSubCategoryResponseDTO> updateSubCategory(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Parent category ID") @PathVariable Long categoryId,
            @Parameter(description = "Sub-category ID") @PathVariable Long subCategoryId,
            @Valid @ModelAttribute ProductSubCategoryRequestDTO subCategoryDTO) {
        return ResponseEntity.ok(categoryService.updateSubCategory(categoryId, subCategoryId, subCategoryDTO));
    }

    @Operation(summary = "Delete a sub-category", description = "Soft deletes a sub-category. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Sub-category deleted successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Sub-category not found")
    })
    @DeleteMapping("/{categoryId}/sub-categories/{subCategoryId}")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteSubCategory(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Parent category ID") @PathVariable Long categoryId,
            @Parameter(description = "Sub-category ID") @PathVariable Long subCategoryId) {
        categoryService.deleteSubCategory(categoryId, subCategoryId, organizationId);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Get a sub-category", description = "Retrieves a sub-category by its ID. Requires authentication.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Sub-category found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Sub-category not found")
    })
    @GetMapping("/{categoryId}/sub-categories/{subCategoryId}")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ProductSubCategoryResponseDTO> getSubCategory(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Parent category ID") @PathVariable Long categoryId,
            @Parameter(description = "Sub-category ID") @PathVariable Long subCategoryId) {
        return ResponseEntity.ok(categoryService.getSubCategory(categoryId, subCategoryId, organizationId));
    }

    @Operation(summary = "Get all sub-categories", description = "Retrieves all sub-categories for a category. If organizationId is provided in the request body, returns sub-categories for that organization and global sub-categories. Otherwise, returns only global sub-categories.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Sub-categories retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Parent category not found")
    })
    @GetMapping("/{categoryId}/sub-categories")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductSubCategoryResponseDTO>> getAllSubCategoriesByCategoryId(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Parent category ID") @PathVariable Long categoryId) {
        return ResponseEntity.ok(categoryService.getAllSubCategoriesByCategoryId(categoryId, organizationId));
    }

    @GetMapping("/sub-categories")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductSubCategoryResponseDTO>> getAllSubCategories(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId) {
        return ResponseEntity.ok(categoryService.getAllSubCategories(organizationId));
    }
}