package com.avinyaops.procurement.product;

import java.util.List;

public interface ProductService {
    ProductResponseDTO createProduct(ProductRequestDTO productRequestDTO);
    
    ProductResponseDTO updateProduct(Long id, ProductRequestDTO productDTO);
    
    void deleteProduct(Long id, Long organizationId);
    
    ProductResponseDTO getProduct(Long id, Long organizationId);
    
    List<ProductResponseDTO> getAllProducts(Long organizationId);
    
    List<ProductResponseDTO> getProductsByCategory(Long categoryId, Long organizationId);
    
    List<ProductResponseDTO> getProductsBySubCategory(Long subCategoryId, Long organizationId);
    
    List<ProductResponseDTO> searchProducts(String query, Long organizationId);
} 