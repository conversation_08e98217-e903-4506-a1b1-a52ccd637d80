package com.avinyaops.procurement.organization;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OrganizationRepository extends JpaRepository<Organization, Long> {

    @Query("SELECT o FROM Organization o")
    @NonNull
    List<Organization> findAll();

    @Query("SELECT o FROM Organization o WHERE o.id = :id")
    @NonNull
    Optional<Organization> findById(@NonNull Long id);

    @Query("SELECT o FROM Organization o WHERE o.name = :name")
    Optional<Organization> findByName(String name);

    @Query("SELECT CASE WHEN COUNT(o) > 0 THEN true ELSE false END FROM Organization o WHERE o.name = :name")
    boolean existsByName(String name);
}