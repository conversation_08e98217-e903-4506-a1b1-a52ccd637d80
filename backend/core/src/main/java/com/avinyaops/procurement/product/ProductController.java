package com.avinyaops.procurement.product;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/v1/organization/{organizationId}/products")
@RequiredArgsConstructor
@Tag(name = "Product Management", description = "APIs for managing products")
public class ProductController {

    private final ProductService productService;

    @Operation(summary = "Create a new product", description = "Creates a new product in the system. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Product created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "409", description = "Product with same name already exists")
    })
    @PostMapping
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ProductResponseDTO> createProduct(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Valid @ModelAttribute ProductRequestDTO productRequestDTO) {
        return ResponseEntity.ok(productService.createProduct(productRequestDTO));
    }

    @Operation(summary = "Update an existing product", description = "Updates an existing product in the system. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Product updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Product not found"),
        @ApiResponse(responseCode = "409", description = "Product with same name already exists")
    })
    @PutMapping("/{id}")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ProductResponseDTO> updateProduct(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Product ID") @PathVariable Long id,
            @Valid @ModelAttribute ProductRequestDTO productRequestDTO) {
        return ResponseEntity.ok(productService.updateProduct(id, productRequestDTO));
    }

    @Operation(summary = "Delete a product", description = "Deletes a product from the system. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Product deleted successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Product not found")
    })
    @DeleteMapping("/{id}")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteProduct(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Product ID") @PathVariable Long id) {
        productService.deleteProduct(id, organizationId);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Get a product by ID", description = "Retrieves a product by its ID. Requires authentication.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Product found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Product not found")
    })
    @GetMapping("/{id}")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ProductResponseDTO> getProduct(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Product ID") @PathVariable Long id) {
        return ResponseEntity.ok(productService.getProduct(id, organizationId));
    }

    @Operation(summary = "Get all products", description = "Retrieves all products. If organizationId is provided in the request body, returns products for that organization and global products. Otherwise, returns only global products.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Products retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductResponseDTO>> getAllProducts(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId) {
        return ResponseEntity.ok(productService.getAllProducts(organizationId));
    }

    //TODO: with reference to the product category controller update this api
    @Operation(summary = "Get products by category", description = "Retrieves all products in a specific category. If organizationId is provided, returns products for that organization and global products. Otherwise, returns only global products.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Products retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Category not found")
    })
    @GetMapping("/category/{categoryId}")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductResponseDTO>> getProductsByCategory(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Category ID") @PathVariable Long categoryId) {
        return ResponseEntity.ok(productService.getProductsByCategory(categoryId, organizationId));
    }

    @Operation(summary = "Get products by sub-category", description = "Retrieves all products in a specific sub-category. If organizationId is provided, returns products for that organization and global products. Otherwise, returns only global products.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Products retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Sub-category not found")
    })
    @GetMapping("/subcategory/{subCategoryId}")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductResponseDTO>> getProductsBySubCategory(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Sub-category ID") @PathVariable Long subCategoryId) {
        return ResponseEntity.ok(productService.getProductsBySubCategory(subCategoryId, organizationId));
    }

    @Operation(summary = "Search products", description = "Searches products by name or description. If organizationId is provided, searches in that organization and global products. Otherwise, searches only in global products.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping("/search")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductResponseDTO>> searchProducts(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId,
            @Parameter(description = "Search query") @RequestParam String query) {
        return ResponseEntity.ok(productService.searchProducts(query, organizationId));
    }
} 