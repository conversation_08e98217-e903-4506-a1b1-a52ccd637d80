package com.avinyaops.procurement.organization.department;

import java.util.List;

public interface DepartmentService {
    DepartmentDTO createDepartment(DepartmentDTO departmentDTO);
    DepartmentDTO updateDepartment(Long id, DepartmentDTO departmentDTO);
    void deleteDepartment(Long id, Long organizationId);
    DepartmentDTO getDepartment(Long id, Long organizationId);
    List<DepartmentDTO> getAllDepartmentsByOrganization(Long organizationId);
} 