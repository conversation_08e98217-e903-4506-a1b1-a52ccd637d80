package com.avinyaops.procurement.email;

import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.validator.routines.EmailValidator;

import com.avinyaops.procurement.exception.AvinyaException;

public class EmailValidationUtil {
    private static final String EMAIL_ERROR_CODE = "INVALID_EMAIL";
    private static final EmailValidator EMAIL_VALIDATOR = EmailValidator.getInstance();
    
    // RFC 5322 compliant email regex pattern
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    public static void validateEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            throw new AvinyaException(EMAIL_ERROR_CODE, "Email address cannot be empty");
        }

        if (!EMAIL_VALIDATOR.isValid(email)) {
            throw new AvinyaException(EMAIL_ERROR_CODE, 
                String.format("Invalid email format: %s", email));
        }

        // Additional validation for maximum length
        if (email.length() > 254) { // RFC 5321
            throw new AvinyaException(EMAIL_ERROR_CODE, 
                String.format("Email address exceeds maximum length of 254 characters: %s", email));
        }
    }

    public static void validateEmailList(List<String> emails) {
        if (emails == null || emails.isEmpty()) {
            return;
        }

        List<String> invalidEmails = emails.stream()
            .filter(email -> !EMAIL_VALIDATOR.isValid(email))
            .collect(Collectors.toList());

        if (!invalidEmails.isEmpty()) {
            throw new AvinyaException(EMAIL_ERROR_CODE, 
                String.format("Invalid email addresses found: %s", String.join(", ", invalidEmails)));
        }
    }

    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_VALIDATOR.isValid(email);
    }

    public static boolean isValidEmailList(List<String> emails) {
        return emails != null && emails.stream().allMatch(EMAIL_VALIDATOR::isValid);
    }
} 