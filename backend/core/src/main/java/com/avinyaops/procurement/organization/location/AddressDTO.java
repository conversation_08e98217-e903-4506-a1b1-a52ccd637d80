package com.avinyaops.procurement.organization.location;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddressDTO {
    @NotEmpty
    @Size(max = 100)
    private String addressLine1;

    @Size(max = 100)
    private String addressLine2;

    @NotEmpty
    @Size(max = 50)
    private String city;

    @Size(max = 50)
    private String state;

    @NotEmpty
    @Size(max = 2)
    @Pattern(regexp = "^[A-Z]{2}$", message = "Country code must be ISO 3166-1 alpha-2")
    private String countryCode;

    @Size(max = 20)
    private String postalCode;

    private Double latitude;
    private Double longitude;
} 