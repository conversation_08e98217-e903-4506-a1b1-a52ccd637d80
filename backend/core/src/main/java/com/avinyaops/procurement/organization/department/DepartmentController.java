package com.avinyaops.procurement.organization.department;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/organization/{organizationId}/departments")
@RequiredArgsConstructor
public class DepartmentController {
    private final DepartmentService departmentService;

    @PostMapping
    public ResponseEntity<DepartmentDTO> create(@PathVariable Long organizationId, @RequestBody DepartmentDTO departmentDTO) {
        return ResponseEntity.ok(departmentService.createDepartment(departmentDTO));
    }

    @PutMapping("/{id}")
    public ResponseEntity<DepartmentDTO> update(@PathVariable Long organizationId, @PathVariable Long id, @RequestBody DepartmentDTO departmentDTO) {
        return ResponseEntity.ok(departmentService.updateDepartment(id, departmentDTO));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long organizationId, @PathVariable Long id) {
        departmentService.deleteDepartment(id, organizationId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}")
    public ResponseEntity<DepartmentDTO> get(@PathVariable Long organizationId, @PathVariable Long id) {
        return ResponseEntity.ok(departmentService.getDepartment(id, organizationId));
    }

    @GetMapping
    public ResponseEntity<List<DepartmentDTO>> getAll(@PathVariable Long organizationId) {
        return ResponseEntity.ok(departmentService.getAllDepartmentsByOrganization(organizationId));
    }
} 