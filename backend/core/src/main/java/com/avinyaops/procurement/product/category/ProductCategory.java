package com.avinyaops.procurement.product.category;

import com.avinyaops.procurement.audit.BaseAuditableEntity;
import com.avinyaops.procurement.idgenerator.AvinyaId;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "product_categories")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@SQLRestriction("deleted_date IS NULL")
public class ProductCategory extends BaseAuditableEntity {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @AvinyaId
    private Long id;

    @NotBlank(message = "Category name is required")
    @Size(max = 100, message = "Category name must be less than 100 characters")
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @NotBlank(message = "Category description is required")
    @Size(max = 500, message = "Category description must be less than 500 characters")
    @Column(name = "description", nullable = false, length = 500)
    private String description;

    @Column(name = "organization_id", nullable = false)
    private Long organizationId;

    @OneToMany(mappedBy = "category", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Builder.Default
    private List<ProductSubCategory> subCategories = new ArrayList<>();

    @Column(name = "image_file_id")
    private String imageFileId;

    public void addSubCategory(ProductSubCategory subCategory) {
        subCategories.add(subCategory);
        subCategory.setCategory(this);
    }

    public void removeSubCategory(ProductSubCategory subCategory) {
        subCategories.remove(subCategory);
        subCategory.setCategory(null);
    }
}
