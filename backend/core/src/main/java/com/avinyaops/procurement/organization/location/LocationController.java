package com.avinyaops.procurement.organization.location;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/organization/{organizationId}/locations")
@RequiredArgsConstructor
@Tag(name = "Location Management", description = "APIs for managing organization locations")
public class LocationController {
    private final LocationService locationService;

    @PostMapping
    @Operation(summary = "Create a new location", description = "Creates a new location for an organization")
    @ApiResponse(responseCode = "201", description = "Location created successfully")
    @ApiResponse(responseCode = "400", description = "Invalid input")
    @ApiResponse(responseCode = "409", description = "Location with same name already exists in the organization")
    public ResponseEntity<LocationDTO> createLocation(@PathVariable Long organizationId, @Valid @RequestBody LocationDTO locationDTO) {
        return new ResponseEntity<>(locationService.createLocation(locationDTO), HttpStatus.CREATED);
    }

    @PutMapping("{id}")
    @Operation(summary = "Update a location", description = "Updates an existing location")
    @ApiResponse(responseCode = "200", description = "Location updated successfully")
    @ApiResponse(responseCode = "400", description = "Invalid input")
    @ApiResponse(responseCode = "404", description = "Location not found")
    @ApiResponse(responseCode = "409", description = "Location with same name already exists in the organization")
    public ResponseEntity<LocationDTO> updateLocation(@PathVariable Long organizationId, @PathVariable Long id, @Valid @RequestBody LocationDTO locationDTO) {
        return ResponseEntity.ok(locationService.updateLocation(id, locationDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a location", description = "Soft deletes a location")
    @ApiResponse(responseCode = "204", description = "Location deleted successfully")
    @ApiResponse(responseCode = "404", description = "Location not found")
    public ResponseEntity<Void> deleteLocation(@PathVariable Long organizationId, @PathVariable Long id) {
        locationService.deleteLocation(id, organizationId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get a location", description = "Retrieves a location by ID")
    @ApiResponse(responseCode = "200", description = "Location found")
    @ApiResponse(responseCode = "404", description = "Location not found")
    public ResponseEntity<LocationDTO> getLocation(@PathVariable Long organizationId, @PathVariable Long id) {
        return ResponseEntity.ok(locationService.getLocationByOrganizationId(id, organizationId));
    }

    @GetMapping
    @Operation(summary = "Get all locations by organization id", description = "Retrieves all active locations")
    @ApiResponse(responseCode = "200", description = "Locations retrieved successfully")
    public ResponseEntity<List<LocationDTO>> getAllLocations(@PathVariable Long organizationId) {
        return ResponseEntity.ok(locationService.getLocationsByOrganization(organizationId));
    }

    @PutMapping("/{id}/set-primary")
    @Operation(summary = "Set a location as primary", description = "Sets a location as primary for an organization")
    @ApiResponse(responseCode = "200", description = "Location set as primary successfully")
    @ApiResponse(responseCode = "404", description = "Location not found")
    public ResponseEntity<LocationDTO> setPrimaryLocation(@PathVariable Long organizationId, @PathVariable Long id) {
        return ResponseEntity.ok(locationService.setPrimaryLocation(id, organizationId));
    }

    @GetMapping("/primary")
    @Operation(summary = "Get primary location for an organization", description = "Retrieves the primary location for an organization")
    @ApiResponse(responseCode = "200", description = "Primary location found")
    @ApiResponse(responseCode = "404", description = "Primary location not found")
    public ResponseEntity<LocationDTO> getPrimaryLocation(@PathVariable Long organizationId) {
        return ResponseEntity.ok(locationService.getPrimaryLocation(organizationId));
    }
} 