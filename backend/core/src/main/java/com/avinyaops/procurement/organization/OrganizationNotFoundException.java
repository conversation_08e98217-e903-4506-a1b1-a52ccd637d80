package com.avinyaops.procurement.organization;

import com.avinyaops.procurement.exception.AvinyaException;

public class OrganizationNotFoundException extends AvinyaException {
    private static final String ERROR_CODE = "ORGANIZATION_NOT_FOUND";

    public OrganizationNotFoundException(Long id) {
        super(ERROR_CODE, String.format("Organization with id %d not found", id));
    }

    public OrganizationNotFoundException(String name) {
        super(ERROR_CODE, String.format("Organization with name %s not found", name));
    }
}
