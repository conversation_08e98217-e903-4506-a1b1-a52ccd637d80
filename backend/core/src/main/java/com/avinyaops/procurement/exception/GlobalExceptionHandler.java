package com.avinyaops.procurement.exception;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.TransactionSystemException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import com.avinyaops.procurement.user.UserAlreadyExistsException;
import com.avinyaops.procurement.user.UserNotFoundException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    //TODO: find out if children of avinya exception even need to be handled or if the below is enough
    @ExceptionHandler(AvinyaException.class)
    public ResponseEntity<ErrorResponse> handleAvinyaException(AvinyaException ex, WebRequest request) {
        log.error("Avinya Exception: ", ex);
        ErrorResponse errorResponse = ErrorResponse.of(ex.getErrorCode(), ex.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleAllUncaughtException(Exception ex, WebRequest request,
            HttpServletRequest httpRequest) {
        log.error("Unknown error occurred: ", ex);
        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(java.time.Instant.now())
                .errorCode("INTERNAL_SERVER_ERROR")
                .message("An unexpected error occurred")
                .path(httpRequest.getRequestURI())
                .build();
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationExceptions(MethodArgumentNotValidException ex,
            WebRequest request) {
        List<String> errors = ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.toList());
        log.error("Validation Exception: ", ex);
        ErrorResponse errorResponse = ErrorResponse.of("VALIDATION_ERROR", "Validation failed", errors);
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(UserAlreadyExistsException.class)
    public ResponseEntity<ErrorResponse> handleUserAlreadyExistsException(UserAlreadyExistsException ex,
            WebRequest request) {
        log.error("User Already Exists Exception: ", ex);
        ErrorResponse errorResponse = ErrorResponse.of(ex.getErrorCode(), ex.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(UserNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleUserNotFoundException(UserNotFoundException ex,
            WebRequest request) {
        log.error("User Not Fount Exception: ", ex);
        ErrorResponse errorResponse = ErrorResponse.of(ex.getErrorCode(), ex.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(TransactionSystemException.class)
    public ResponseEntity<ErrorResponse> handleTransactionSystemException(TransactionSystemException ex,
            WebRequest request, HttpServletRequest httpRequest) {

        // Unwrap the root cause
        Throwable root = ex.getRootCause();
        while (root != null && !(root instanceof ConstraintViolationException)) {
            root = root.getCause();
        }

        // If it's a bean-validation error, delegate to the constraint violation handler
        if (root instanceof ConstraintViolationException) {
            return handleConstraintViolationException((ConstraintViolationException) root, request, httpRequest);
        }

        // Otherwise, return a generic error
        log.error("Transaction System Exception: ", ex);

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(java.time.Instant.now())
                .errorCode("TRANSACTION_ERROR")
                .message("Transaction failed")
                .details(List.of(ex.getMostSpecificCause().getMessage()))
                .path(httpRequest.getRequestURI())
                .build();

        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ErrorResponse> handleConstraintViolationException(ConstraintViolationException ex,
            WebRequest request, HttpServletRequest httpRequest) {
        log.error("Constraint Violation Exception: ", ex);

        List<String> errors = new ArrayList<>();
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            errors.add(violation.getPropertyPath() + ": " + violation.getMessage());
        }

        ErrorResponse errorResponse = ErrorResponse.of("VALIDATION_ERROR", "Validation constraints violated", errors);
        errorResponse.setPath(httpRequest.getRequestURI());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }
}