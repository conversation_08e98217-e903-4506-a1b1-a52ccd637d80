package com.avinyaops.procurement.product.category;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProductSubCategoryRepository extends JpaRepository<ProductSubCategory, Long> {

    @Query("SELECT s FROM ProductSubCategory s WHERE s.organizationId = :organizationId")
    List<ProductSubCategory> findAllByOrganizationId(Long organizationId);

    @Query("SELECT s FROM ProductSubCategory s WHERE s.id = :id")
    @NonNull
    Optional<ProductSubCategory> findById(@NonNull Long id);

    Optional<ProductSubCategory> findByIdAndOrganizationId(Long id, Long organizationId);

    @Query("SELECT s FROM ProductSubCategory s WHERE s.category.id = :categoryId " +
            "AND s.organizationId = :organizationId")
    List<ProductSubCategory> findAllByCategoryIdAndOrganizationId(Long categoryId, Long organizationId);

    @Query("SELECT CASE WHEN COUNT(sc) > 0 THEN true ELSE false END FROM ProductSubCategory sc " +
            "WHERE sc.name = :name AND sc.category.id = :categoryId " +
            "AND sc.organizationId = :organizationId")
    boolean existsByNameAndCategoryIdAndOrganizationId(String name, Long categoryId, Long organizationId);

    //TODO: cleanup this interface
    List<ProductSubCategory> findAllByCategoryId(Long categoryId);
    
    //TODO: find if below methods are needed or not
    @Query("SELECT s FROM ProductSubCategory s WHERE s.category.id = :categoryId " +
            "AND (s.organizationId = :organizationId OR s.organizationId IS NULL)")
    List<ProductSubCategory> findAllByCategoryIdAndOrganizationIdOrCategoryIdAndOrganizationIdIsNull(
            @Param("categoryId") Long categoryId,
            @Param("organizationId") Long organizationId);

    @Query("SELECT sc FROM ProductSubCategory sc WHERE sc.category.id = :categoryId " +
            "AND sc.organizationId IS NULL")
    List<ProductSubCategory> findAllByCategoryIdAndOrganizationIdIsNull(Long categoryId);
}
