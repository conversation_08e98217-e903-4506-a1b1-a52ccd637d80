package com.avinyaops.procurement.organization.location;

import java.util.List;

public interface LocationService {
    LocationDTO createLocation(LocationDTO locationDTO);

    LocationDTO updateLocation(Long id, LocationDTO locationDTO);

    void deleteLocation(Long id, Long organizationId);

    LocationDTO getLocationByOrganizationId(Long id, Long organizationId);

    List<LocationDTO> getLocationsByOrganization(Long organizationId);

    LocationDTO setPrimaryLocation(Long id, Long organizationId);

    LocationDTO getPrimaryLocation(Long organizationId);
}