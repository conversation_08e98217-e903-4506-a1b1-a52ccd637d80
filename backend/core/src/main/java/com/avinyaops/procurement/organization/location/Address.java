package com.avinyaops.procurement.organization.location;

import java.io.Serializable;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * Value object representing a postal address.
 */
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder
@EqualsAndHashCode
public class Address implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotEmpty
    @Size(max = 100)
    @Column(name = "address_line1", length = 100, nullable = false)
    private String addressLine1;

    @Size(max = 100)
    @Column(name = "address_line2", length = 100)
    private String addressLine2;

    @NotEmpty
    @Size(max = 50)
    @Column(name = "address_city", length = 50, nullable = false)
    private String city;

    @NotEmpty
    @Size(max = 50)
    @Column(name = "address_state", length = 50)
    private String state;

    @NotEmpty
    @Size(max = 2)
    @Pattern(regexp = "^[A-Z]{2}$", message = "Country code must be ISO 3166-1 alpha-2")
    @Column(name = "address_country_code", length = 2, nullable = false)
    private String countryCode;

    @Size(max = 20)
    @Column(name = "address_postal_code", length = 20)
    private String postalCode;

    @Column(name = "address_latitude")
    private Double latitude;

    @Column(name = "address_longitude")
    private Double longitude;
}
