package com.avinyaops.procurement.product;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {

        Optional<Product> findByIdAndOrganizationId(Long id, Long organizationId);

        List<Product> findByOrganizationId(Long organizationId);

        List<Product> findByCategoryIdAndOrganizationId(Long categoryId, Long organizationId);

        List<Product> findBySubCategoryIdAndOrganizationId(Long subCategoryId, Long organizationId);

        boolean existsByNameAndOrganizationId(String name, Long organizationId);

        @Query("SELECT p FROM Product p WHERE p.organizationId = :organizationId AND " +
                        "(LOWER(p.name) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
                        "LOWER(p.description) LIKE LOWER(CONCAT('%', :query, '%')))")
        List<Product> searchProducts(@Param("query") String query, @Param("organizationId") Long organizationId);

        @Query("SELECT p FROM Product p WHERE p.organizationId = :organizationId OR p.organizationId IS NULL")
        List<Product> findAllByOrganizationIdOrOrganizationIdIsNull(@Param("organizationId") Long organizationId);

        List<Product> findAllByOrganizationId(@Param("organizationId") Long organizationId);

        @Query("SELECT p FROM Product p WHERE p.organizationId IS NULL")
        List<Product> findAllByOrganizationIdIsNull();

        @Query("SELECT p FROM Product p WHERE p.category.id = :categoryId AND (p.organizationId = :organizationId OR p.organizationId IS NULL)")
        List<Product> findAllByCategoryIdAndOrganizationIdOrCategoryIdAndOrganizationIdIsNull(
                        @Param("categoryId") Long categoryId,
                        @Param("organizationId") Long organizationId);

        @Query("SELECT p FROM Product p WHERE p.category.id = :categoryId AND p.organizationId IS NULL")
        List<Product> findAllByCategoryIdAndOrganizationIdIsNull(@Param("categoryId") Long categoryId);

        List<Product> findAllByCategoryIdAndOrganizationId(@Param("categoryId") Long categoryId, @Param("organizationId") Long organizationId);

        @Query("SELECT p FROM Product p WHERE p.subCategory.id = :subCategoryId AND (p.organizationId = :organizationId OR p.organizationId IS NULL)")
        List<Product> findAllBySubCategoryIdAndOrganizationIdOrSubCategoryIdAndOrganizationIdIsNull(
                        @Param("subCategoryId") Long subCategoryId,
                        @Param("organizationId") Long organizationId);

        @Query("SELECT p FROM Product p WHERE p.subCategory.id = :subCategoryId AND p.organizationId IS NULL")
        List<Product> findAllBySubCategoryIdAndOrganizationIdIsNull(@Param("subCategoryId") Long subCategoryId);

        List<Product> findAllBySubCategoryIdAndOrganizationId(@Param("subCategoryId") Long subCategoryId, @Param("organizationId") Long organizationId);

        @Query("SELECT p FROM Product p WHERE (LOWER(p.name) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(p.description) LIKE LOWER(CONCAT('%', :query, '%'))) AND (p.organizationId = :organizationId OR p.organizationId IS NULL)")
        List<Product> searchByNameOrDescriptionAndOrganizationIdOrOrganizationIdIsNull(
                        @Param("query") String query,
                        @Param("organizationId") Long organizationId);

        @Query("SELECT p FROM Product p WHERE (LOWER(p.name) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(p.description) LIKE LOWER(CONCAT('%', :query, '%'))) AND p.organizationId IS NULL")
        List<Product> searchByNameOrDescriptionAndOrganizationIdIsNull(@Param("query") String query);

        @Query("SELECT p FROM Product p WHERE (LOWER(p.name) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(p.description) LIKE LOWER(CONCAT('%', :query, '%'))) AND p.organizationId = :organizationId")
        List<Product> searchByNameOrDescriptionAndOrganizationId(@Param("query") String query, @Param("organizationId") Long organizationId);
}
