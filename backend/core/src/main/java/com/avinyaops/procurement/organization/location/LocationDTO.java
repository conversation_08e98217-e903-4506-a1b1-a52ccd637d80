package com.avinyaops.procurement.organization.location;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationDTO {
    // ID is only used in responses, not in requests
    // TODO: will need to make seperate dtos with not null and without validation
    private Long id;

    @NotBlank(message = "Location name is required")
    @Size(max = 100, message = "Location name must be less than 100 characters")
    private String name;

    @NotBlank(message = "Timezone is required")
    @Size(max = 50, message = "Timezone must be less than 50 characters")
    private String timezone;

    @NotNull(message = "Organization ID is required")
    private Long organizationId;

    @NotNull(message = "Address is required")
    private AddressDTO address;

    private boolean isPrimary;
} 