package com.avinyaops.procurement.email;

import java.util.concurrent.CompletableFuture;

import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {

    private final JavaMailSender mailSender;
    private final SpringTemplateEngine templateEngine;
    private final EmailConfig emailConfig;

    @Override
    public void sendEmail(EmailRequest request) {
        validateEmailRequest(request);
        try {
            MimeMessage message = prepareMessage(request);
            mailSender.send(message);
            log.info("Email sent successfully to: {}", request.getTo());
        } catch (Exception e) {
            log.error("Failed to send email to: {}", request.getTo(), e);
            throw new RuntimeException("Failed to send email", e);
        }
    }

    @Override
    @Async
    public void sendEmailAsync(EmailRequest request) {
        CompletableFuture.runAsync(() -> sendEmail(request));
    }

    private void validateEmailRequest(EmailRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("Email request cannot be null");
        }

        // Validate recipient
        EmailValidationUtil.validateEmail(request.getTo());

        // Validate CC recipients if present
        if (request.getCc() != null && !request.getCc().isEmpty()) {
            EmailValidationUtil.validateEmailList(request.getCc());
        }

        // Validate BCC recipients if present
        if (request.getBcc() != null && !request.getBcc().isEmpty()) {
            EmailValidationUtil.validateEmailList(request.getBcc());
        }

        // Validate reply-to if present
        if (request.getReplyTo() != null && !request.getReplyTo().isEmpty()) {
            EmailValidationUtil.validateEmail(request.getReplyTo());
        }

        // Validate subject
        if (request.getSubject() == null || request.getSubject().trim().isEmpty()) {
            throw new IllegalArgumentException("Email subject cannot be empty");
        }

        // Validate template if HTML is true
        if (request.isHtml() && (request.getTemplateName() == null || request.getTemplateName().trim().isEmpty())) {
            throw new IllegalArgumentException("Template name is required for HTML emails");
        }
    }

    private MimeMessage prepareMessage(EmailRequest request) throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

        helper.setFrom(emailConfig.getFrom());
        helper.setTo(request.getTo());
        helper.setSubject(request.getSubject());

        if (request.getCc() != null && !request.getCc().isEmpty()) {
            helper.setCc(request.getCc().toArray(new String[0]));
        }

        if (request.getBcc() != null && !request.getBcc().isEmpty()) {
            helper.setBcc(request.getBcc().toArray(new String[0]));
        }

        if (request.getReplyTo() != null) {
            helper.setReplyTo(request.getReplyTo());
        }

        // Process template if provided
        if (request.getTemplateName() != null) {
            Context context = new Context();
            if (request.getTemplateData() != null) {
                request.getTemplateData().forEach(context::setVariable);
            }
            String content = templateEngine.process(request.getTemplateName(), context);
            helper.setText(content, request.isHtml());
        }

        // Add attachments if any
        if (request.getAttachments() != null) {
            for (EmailAttachment attachment : request.getAttachments()) {
                helper.addAttachment(attachment.getFileName(), 
                    () -> new java.io.ByteArrayInputStream(attachment.getContent()),
                    attachment.getContentType());
            }
        }

        return message;
    }
} 