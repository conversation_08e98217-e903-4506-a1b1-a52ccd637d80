package com.avinyaops.procurement.organization.designation;

import java.util.List;

public interface DesignationService {
    DesignationDTO createDesignation(DesignationDTO designationDTO);
    DesignationDTO updateDesignation(Long id, DesignationDTO designationDTO);
    void deleteDesignation(Long id, Long organizationId);
    DesignationDTO getDesignation(Long id, Long organizationId);
    List<DesignationDTO> getDesignationsByOrganization(Long organizationId);
} 