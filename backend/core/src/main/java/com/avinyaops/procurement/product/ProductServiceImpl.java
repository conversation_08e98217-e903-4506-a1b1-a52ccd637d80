package com.avinyaops.procurement.product;

import com.avinyaops.fileupload.FileStorageService;
import com.avinyaops.procurement.product.category.ProductCategory;
import com.avinyaops.procurement.product.category.ProductCategoryRepository;
import com.avinyaops.procurement.product.category.ProductSubCategory;
import com.avinyaops.procurement.product.category.ProductSubCategoryRepository;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ProductServiceImpl implements ProductService {

    private final ProductRepository productRepository;
    private final ProductCategoryRepository categoryRepository;
    private final ProductSubCategoryRepository subCategoryRepository;
    private final FileStorageService fileStorageService;
    private final String productImageBaseFolderPath = "product";

    @Override
    public ProductResponseDTO createProduct(ProductRequestDTO productRequestDTO) {
        // Check if product with same name exists for the organization
        if (productRepository.existsByNameAndOrganizationId(productRequestDTO.getName(),
                productRequestDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException(
                    "Product with name " + productRequestDTO.getName() + " already exists");
        }

        // Validate category and sub-category
        ProductCategory category = categoryRepository
                .findByIdAndOrganizationId(productRequestDTO.getCategoryId(), productRequestDTO.getOrganizationId())
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Category not found with id: " + productRequestDTO.getCategoryId()));

        ProductSubCategory subCategory = null;
        if (productRequestDTO.getSubCategoryId() != null) {
            subCategory = subCategoryRepository
                    .findByIdAndOrganizationId(productRequestDTO.getSubCategoryId(),
                            productRequestDTO.getOrganizationId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Sub-category not found with id: "
                                    + productRequestDTO.getSubCategoryId()));

            // Validate sub-category belongs to the category
            if (!subCategory.getCategory().getId().equals(category.getId())) {
                throw new IllegalArgumentException(
                        "Sub-category does not belong to the specified category");
            }
        }

        Product product = Product.builder()
                .name(productRequestDTO.getName())
                .description(productRequestDTO.getDescription())
                .category(category)
                .subCategory(subCategory)
                .organizationId(productRequestDTO.getOrganizationId())
                .imageFileId(Optional.ofNullable(productRequestDTO.getImageFile())
                        .map(imageFile -> fileStorageService
                                .uploadFile(imageFile, productImageBaseFolderPath)
                                .getFileId())
                        .orElse(null))
                .build();

        return mapToDTO(productRepository.save(product));
    }

    @Override
    public ProductResponseDTO updateProduct(Long id, ProductRequestDTO productRequestDTO) {
        Product product = productRepository.findByIdAndOrganizationId(id, productRequestDTO.getOrganizationId())
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + id));

        // Check if another product with same name exists for the organization
        if (!product.getName().equals(productRequestDTO.getName()) &&
                productRepository.existsByNameAndOrganizationId(productRequestDTO.getName(),
                        productRequestDTO.getOrganizationId())) {
            throw new ResourceAlreadyExistsException(
                    "Product with name " + productRequestDTO.getName() + " already exists");
        }

        // Validate category and sub-category
        ProductCategory category = categoryRepository
                .findByIdAndOrganizationId(productRequestDTO.getCategoryId(), productRequestDTO.getOrganizationId())
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Category not found with id: " + productRequestDTO.getCategoryId()));

        ProductSubCategory subCategory = null;
        if (productRequestDTO.getSubCategoryId() != null) {
            subCategory = subCategoryRepository
                    .findByIdAndOrganizationId(productRequestDTO.getSubCategoryId(),
                            productRequestDTO.getOrganizationId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Sub-category not found with id: "
                                    + productRequestDTO.getSubCategoryId()));

            // Validate sub-category belongs to the category
            if (!subCategory.getCategory().getId().equals(category.getId())) {
                throw new IllegalArgumentException(
                        "Sub-category does not belong to the specified category");
            }
        }

        product.setName(productRequestDTO.getName());
        product.setDescription(productRequestDTO.getDescription());
        product.setCategory(category);
        product.setSubCategory(subCategory);
        product.setOrganizationId(productRequestDTO.getOrganizationId());

        if (productRequestDTO.getImageFile() != null && !productRequestDTO.getImageFile().isEmpty()) {
            String newImageFileIds = fileStorageService
                    .uploadFile(productRequestDTO.getImageFile(), productImageBaseFolderPath)
                    .getFileId();
            product.setImageFileId(newImageFileIds);
        } else if (productRequestDTO.isDeleteImage() == true && product.getImageFileId() != null
                && !product.getImageFileId().isEmpty()) {
            fileStorageService.deleteFile(product.getImageFileId());
            product.setImageFileId(null);
        }

        return mapToDTO(productRepository.save(product));
    }

    @Override
    public void deleteProduct(Long id, Long organizationId) {
        Product product = productRepository.findByIdAndOrganizationId(id, organizationId)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + id));
        if (product.getImageFileId() != null && !product.getImageFileId().isBlank()) {
            fileStorageService.deleteFile(product.getImageFileId());
        }

        product.softDelete();
        productRepository.save(product);
    }

    @Override
    public ProductResponseDTO getProduct(Long id, Long organizationId) {
        return productRepository.findByIdAndOrganizationId(id, organizationId)
                .map(this::mapToDTO)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + id));
    }

    @Override
    public List<ProductResponseDTO> getAllProducts(Long organizationId) {
        return productRepository.findAllByOrganizationId(organizationId)
                .stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductResponseDTO> getProductsByCategory(Long categoryId, Long organizationId) {
        return productRepository.findAllByCategoryIdAndOrganizationId(categoryId, organizationId)
                .stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductResponseDTO> getProductsBySubCategory(Long subCategoryId, Long organizationId) {
        return productRepository.findAllBySubCategoryIdAndOrganizationId(subCategoryId, organizationId)
                .stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductResponseDTO> searchProducts(String query, Long organizationId) {
        return productRepository.searchByNameOrDescriptionAndOrganizationId(query, organizationId)
                .stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    private ProductResponseDTO mapToDTO(Product product) {
        return ProductResponseDTO.builder()
                .id(product.getId())
                .name(product.getName())
                .description(product.getDescription())
                .categoryId(product.getCategory().getId())
                .subCategoryId(product.getSubCategory() != null ? product.getSubCategory().getId()
                        : null)
                .imageUrl(
                        Optional.ofNullable(product.getImageFileId())
                                .filter(fileId -> !fileId.isBlank())
                                .map(fileStorageService::generateViewUrl)
                                .orElse(null))
                .organizationId(product.getOrganizationId())
                .build();
    }
}