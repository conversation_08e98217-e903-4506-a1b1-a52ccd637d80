package com.avinyaops.procurement.organization;

import java.util.List;

public interface OrganizationService {
    OrganizationDetailResponse createOrganization(OrganizationCreateUpdateRequest request);
    
    OrganizationDetailResponse updateOrganization(Long id, OrganizationCreateUpdateRequest request);
    
    void deleteOrganization(Long id);
    
    OrganizationDetailResponse getOrganization(Long id);
    
    List<OrganizationDetailResponse> getAllOrganizationsDetail();
    
    List<OrganizationShortResponse> getAllOrganizationsShort();
    
    // Method to get Organization entity directly
    Organization getOrganizationEntity(Long id);
}
